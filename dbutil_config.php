<?php
/**
 * Database Utility Configuration
 * 
 * This file controls access to the database utility script.
 * Set DB_UTIL_ALLOWED to true to enable the utility.
 */

// Enable database utility access
// Set to true only when you need to run database migrations
// Remember to set back to false after use for security
define('DB_UTIL_ALLOWED', true);

// Allowed IP addresses (in addition to localhost)
// Add your server's IP or your development machine's IP
$allowed_ips = [
    '127.0.0.1',
    '::1',
    'localhost',
    // Add your IPs here:
    // '*************',
    // '*********',
];

// Check access
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
$is_allowed_ip = in_array($client_ip, $allowed_ips);
$has_access_key = isset($_GET['key']) && $_GET['key'] === 'abla2024'; // Change this key

if (!DB_UTIL_ALLOWED && !$is_allowed_ip && !$has_access_key) {
    http_response_code(403);
    die('Access denied. Database utility is disabled.');
}

// Include the main utility
require_once 'dbutil.php';
?>
