<?php
/**
 * Database Debug Script
 * 
 * This script helps debug database issues by showing detailed information
 * about the current database state and any errors during initialization.
 */

require_once 'includes/db.php';

// HTML output
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Debug - ABLA</title>
    <style>
        body { 
            font-family: 'Courier New', monospace; 
            background: #1a1a1a; 
            color: #fff; 
            margin: 0; 
            padding: 20px; 
            line-height: 1.6;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: #2a2a2a; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        h1 { 
            color: #f7931a; 
            text-align: center; 
            margin-bottom: 30px;
        }
        h2 { 
            color: #f7931a; 
            border-bottom: 2px solid #f7931a; 
            padding-bottom: 10px;
        }
        .success { 
            background: #1a4a1a; 
            border: 1px solid #4a8a4a; 
            color: #8af88a; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
        }
        .error { 
            background: #4a1a1a; 
            border: 1px solid #8a4a4a; 
            color: #f88a8a; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
        }
        .info { 
            background: #1a1a4a; 
            border: 1px solid #4a4a8a; 
            color: #8a8af8; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
        }
        .code { 
            background: #000; 
            border: 1px solid #444; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto; 
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 20px 0; 
        }
        th, td { 
            border: 1px solid #444; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background: #333; 
            color: #f7931a; 
        }
        .btn { 
            background: #f7931a; 
            color: #000; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-weight: bold; 
            text-decoration: none; 
            display: inline-block; 
            margin: 10px 5px;
        }
        .btn:hover { 
            background: #ff9500; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Database Debug - ABLA</h1>
        
        <?php
        // Test database connection
        echo "<h2>1. Database Connection Test</h2>";
        
        $db = connectDB();
        if (is_array($db)) {
            echo "<div class=\"error\">❌ Connection failed: " . $db['error'] . "</div>";
            if (isset($db['error_details'])) {
                echo "<div class=\"code\">" . $db['error_details'] . "</div>";
            }
            exit;
        } else {
            echo "<div class=\"success\">✅ Database connection successful</div>";
        }
        
        // Get database name
        $result = $db->query("SELECT DATABASE() as db_name");
        $db_name = $result ? $result->fetch_assoc()['db_name'] : 'Unknown';
        echo "<div class=\"info\">Database: $db_name</div>";
        
        // Show all existing tables
        echo "<h2>2. Existing Tables</h2>";
        $result = $db->query("SHOW TABLES");
        if ($result && $result->num_rows > 0) {
            echo "<table>";
            echo "<tr><th>Table Name</th><th>Rows</th><th>Status</th></tr>";
            
            while ($row = $result->fetch_array()) {
                $table_name = $row[0];
                
                // Get row count
                $count_result = $db->query("SELECT COUNT(*) as count FROM `$table_name`");
                $row_count = $count_result ? $count_result->fetch_assoc()['count'] : 'Error';
                
                echo "<tr>";
                echo "<td>$table_name</td>";
                echo "<td>$row_count</td>";
                echo "<td>✅ Exists</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class=\"error\">❌ No tables found in database</div>";
        }
        
        // Check required tables
        echo "<h2>3. Required Tables Check</h2>";
        $required_tables = ['content', 'initiatives', 'events', 'contacts', 'users', 'themes', 'messages'];
        
        echo "<table>";
        echo "<tr><th>Table</th><th>Status</th><th>Action</th></tr>";
        
        foreach ($required_tables as $table) {
            $exists = tableExists($db, $table);
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td>" . ($exists ? "✅ Exists" : "❌ Missing") . "</td>";
            echo "<td>" . ($exists ? "OK" : "Needs creation") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test database initialization
        if (isset($_GET['test_init'])) {
            echo "<h2>4. Database Initialization Test</h2>";
            
            $result = initializeDatabase($db);
            
            if ($result['success']) {
                echo "<div class=\"success\">✅ Database initialization completed successfully</div>";
                
                if (!empty($result['created_tables'])) {
                    echo "<div class=\"info\">Created tables: " . implode(', ', $result['created_tables']) . "</div>";
                }
                
                if (!empty($result['sample_data_added'])) {
                    echo "<div class=\"info\">Sample data added:</div>";
                    echo "<ul>";
                    foreach ($result['sample_data_added'] as $item) {
                        echo "<li>$item</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<div class=\"error\">❌ Database initialization failed</div>";
                
                if (!empty($result['errors'])) {
                    echo "<div class=\"error\">Errors:</div>";
                    echo "<ul>";
                    foreach ($result['errors'] as $error) {
                        echo "<li>$error</li>";
                    }
                    echo "</ul>";
                }
            }
            
            // Refresh page to show updated table status
            echo "<script>setTimeout(() => window.location.href = window.location.pathname, 2000);</script>";
        }
        
        // Show table structures
        echo "<h2>5. Table Structures</h2>";
        $result = $db->query("SHOW TABLES");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_array()) {
                $table_name = $row[0];
                echo "<h3>Table: $table_name</h3>";
                
                $structure_result = $db->query("DESCRIBE `$table_name`");
                if ($structure_result) {
                    echo "<table>";
                    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                    
                    while ($field = $structure_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>{$field['Field']}</td>";
                        echo "<td>{$field['Type']}</td>";
                        echo "<td>{$field['Null']}</td>";
                        echo "<td>{$field['Key']}</td>";
                        echo "<td>{$field['Default']}</td>";
                        echo "<td>{$field['Extra']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
        
        // Actions
        echo "<h2>6. Actions</h2>";
        echo "<a href=\"?test_init=1\" class=\"btn\">🔧 Test Database Initialization</a>";
        echo "<a href=\"/?route=admin\" class=\"btn\">🏠 Go to Admin</a>";
        echo "<a href=\"/?route=admin&page=db_init\" class=\"btn\">🗄️ Database Init Page</a>";
        echo "<a href=\"/update_db.php\" class=\"btn\">⚡ Database Utility</a>";
        
        // Show PHP and MySQL versions
        echo "<h2>7. System Information</h2>";
        echo "<div class=\"info\">";
        echo "PHP Version: " . phpversion() . "<br>";
        echo "MySQL Version: " . $db->server_info . "<br>";
        echo "MySQL Client Version: " . $db->client_info . "<br>";
        echo "</div>";
        ?>
    </div>
</body>
</html>
