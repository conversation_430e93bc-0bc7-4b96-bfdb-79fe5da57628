<?php
/**
 * Database Migration: Add Avatar and X/Twitter Support to Initiatives
 * 
 * This script adds new fields to the initiatives table:
 * - twitter_user: X (Twitter) username
 * - twitter_link: X (Twitter) profile URL
 * - avatar_url: Profile picture URL
 * - avatar_source: Source of avatar (instagram, twitter, manual)
 */

require_once 'includes/db.php';

echo "<h1>Database Migration: Initiatives Avatar Support</h1>";

$db = connectDB();
if (is_array($db)) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $db['error'] . "</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected successfully</p>";

// Check if columns already exist
$columns_to_add = [
    'twitter_user' => "VARCHAR(100) DEFAULT NULL",
    'twitter_link' => "VARCHAR(255) DEFAULT NULL", 
    'avatar_url' => "VARCHAR(500) DEFAULT NULL",
    'avatar_source' => "ENUM('instagram', 'twitter', 'manual') DEFAULT 'instagram'"
];

$existing_columns = [];
$result = $db->query("DESCRIBE initiatives");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
}

echo "<h2>Current Initiatives Table Structure:</h2>";
echo "<ul>";
foreach ($existing_columns as $column) {
    echo "<li>$column</li>";
}
echo "</ul>";

echo "<h2>Adding New Columns:</h2>";

$added_columns = [];
$errors = [];

foreach ($columns_to_add as $column_name => $column_definition) {
    if (!in_array($column_name, $existing_columns)) {
        $sql = "ALTER TABLE initiatives ADD COLUMN `$column_name` $column_definition";
        
        if ($db->query($sql)) {
            echo "<p style='color: green;'>✅ Added column: $column_name</p>";
            $added_columns[] = $column_name;
        } else {
            echo "<p style='color: red;'>❌ Failed to add column $column_name: " . $db->error . "</p>";
            $errors[] = "Failed to add column $column_name: " . $db->error;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ Column $column_name already exists</p>";
    }
}

// Show updated table structure
echo "<h2>Updated Table Structure:</h2>";
$result = $db->query("DESCRIBE initiatives");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        $is_new = in_array($row['Field'], $added_columns);
        $style = $is_new ? "background-color: #e8f5e8;" : "";
        
        echo "<tr style='$style'>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

if (empty($errors)) {
    echo "<h2 style='color: green;'>✅ Migration completed successfully!</h2>";
    echo "<p>You can now:</p>";
    echo "<ul>";
    echo "<li>Add X/Twitter usernames and links to initiatives</li>";
    echo "<li>Store avatar URLs for profile pictures</li>";
    echo "<li>Specify the source of the avatar (Instagram, Twitter, or manual)</li>";
    echo "</ul>";
} else {
    echo "<h2 style='color: red;'>❌ Migration completed with errors:</h2>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

echo "<p><a href='/admin/dashboard.php'>← Back to Admin Dashboard</a></p>";
echo "<p><a href='/debug_db.php'>🔍 Debug Database</a></p>";
?>
