<?php
/**
 * Database Backup Utility
 * 
 * Creates a backup of the current database before applying updates.
 * This ensures you can restore your data if something goes wrong.
 */

require_once 'includes/db.php';

// Get database configuration
$config = getDatabaseConfig();

if (!$config) {
    die("Error: Could not load database configuration.\n");
}

// Create backup filename with timestamp
$timestamp = date('Y-m-d_H-i-s');
$backup_filename = "backup_abla_{$timestamp}.sql";

// Check if we're running from command line or web
$is_cli = php_sapi_name() === 'cli';

if (!$is_cli) {
    // Web interface
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Database Backup - ABLA</title>
        <style>
            body { 
                font-family: 'Courier New', monospace; 
                background: #1a1a1a; 
                color: #fff; 
                margin: 0; 
                padding: 20px; 
                line-height: 1.6;
            }
            .container { 
                max-width: 800px; 
                margin: 0 auto; 
                background: #2a2a2a; 
                padding: 30px; 
                border-radius: 10px; 
                box-shadow: 0 0 20px rgba(0,0,0,0.5);
            }
            h1 { 
                color: #f7931a; 
                text-align: center; 
                margin-bottom: 30px;
            }
            .success { 
                background: #1a4a1a; 
                border: 1px solid #4a8a4a; 
                color: #8af88a; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .error { 
                background: #4a1a1a; 
                border: 1px solid #8a4a4a; 
                color: #f88a8a; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .info { 
                background: #1a1a4a; 
                border: 1px solid #4a4a8a; 
                color: #8a8af8; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .btn { 
                background: #f7931a; 
                color: #000; 
                padding: 12px 24px; 
                border: none; 
                border-radius: 5px; 
                cursor: pointer; 
                font-weight: bold; 
                text-decoration: none; 
                display: inline-block; 
                margin: 10px 5px;
            }
            .btn:hover { 
                background: #ff9500; 
            }
            .code { 
                background: #000; 
                border: 1px solid #444; 
                padding: 15px; 
                border-radius: 5px; 
                overflow-x: auto; 
                margin: 10px 0;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>💾 Database Backup Utility</h1>
    <?php
}

function output($message, $type = 'info') {
    global $is_cli;
    
    if ($is_cli) {
        $prefix = match($type) {
            'success' => '[✓] ',
            'error' => '[✗] ',
            default => '[i] '
        };
        echo $prefix . strip_tags($message) . "\n";
    } else {
        echo "<div class=\"$type\">$message</div>\n";
    }
}

try {
    output("Iniciando backup de la base de datos...", 'info');
    output("Base de datos: {$config['database']}", 'info');
    output("Archivo de backup: $backup_filename", 'info');
    
    // Check if mysqldump is available
    $mysqldump_path = 'mysqldump';
    
    // Try to find mysqldump in common locations
    $possible_paths = [
        'mysqldump',
        '/usr/bin/mysqldump',
        '/usr/local/bin/mysqldump',
        '/opt/lampp/bin/mysqldump',
        'C:\\xampp\\mysql\\bin\\mysqldump.exe',
        'C:\\wamp\\bin\\mysql\\mysql8.0.21\\bin\\mysqldump.exe'
    ];
    
    foreach ($possible_paths as $path) {
        if (is_executable($path) || (PHP_OS_FAMILY === 'Windows' && is_file($path))) {
            $mysqldump_path = $path;
            break;
        }
    }
    
    // Build mysqldump command
    $command = sprintf(
        '%s --host=%s --port=%d --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
        escapeshellarg($mysqldump_path),
        escapeshellarg($config['host']),
        $config['port'],
        escapeshellarg($config['username']),
        escapeshellarg($config['password']),
        escapeshellarg($config['database']),
        escapeshellarg($backup_filename)
    );
    
    if (!$is_cli) {
        output("Comando a ejecutar:", 'info');
        // Don't show password in web interface
        $safe_command = str_replace($config['password'], '***', $command);
        echo "<div class=\"code\">$safe_command</div>";
    }
    
    // Execute backup
    output("Ejecutando backup...", 'info');
    
    $output_lines = [];
    $return_code = 0;
    exec($command . ' 2>&1', $output_lines, $return_code);
    
    if ($return_code === 0 && file_exists($backup_filename)) {
        $file_size = filesize($backup_filename);
        $file_size_mb = round($file_size / 1024 / 1024, 2);
        
        output("✓ Backup completado exitosamente!", 'success');
        output("Archivo: $backup_filename", 'success');
        output("Tamaño: {$file_size_mb} MB", 'success');
        
        if (!$is_cli) {
            echo "<div class=\"info\">";
            echo "<strong>Instrucciones para restaurar:</strong><br>";
            echo "Si necesitas restaurar este backup, ejecuta:<br>";
            echo "<div class=\"code\">mysql -u {$config['username']} -p {$config['database']} < $backup_filename</div>";
            echo "</div>";
            
            echo "<div>";
            echo "<a href=\"$backup_filename\" class=\"btn\" download>Descargar Backup</a>";
            echo "<a href=\"update_db.php\" class=\"btn\">Continuar con Actualización</a>";
            echo "</div>";
        }
        
    } else {
        output("✗ Error durante el backup", 'error');
        
        if (!empty($output_lines)) {
            output("Salida del comando:", 'error');
            foreach ($output_lines as $line) {
                output($line, 'error');
            }
        }
        
        // Try alternative backup method using PHP
        output("Intentando método alternativo...", 'info');
        
        $db = connectDB();
        if (!is_array($db)) {
            $backup_content = "-- ABLA Database Backup\n";
            $backup_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
            
            // Get all tables
            $tables_result = $db->query("SHOW TABLES");
            $tables = [];
            
            while ($row = $tables_result->fetch_array()) {
                $tables[] = $row[0];
            }
            
            foreach ($tables as $table) {
                $backup_content .= "\n-- Table: $table\n";
                
                // Get table structure
                $create_result = $db->query("SHOW CREATE TABLE `$table`");
                if ($create_row = $create_result->fetch_array()) {
                    $backup_content .= "DROP TABLE IF EXISTS `$table`;\n";
                    $backup_content .= $create_row[1] . ";\n\n";
                }
                
                // Get table data
                $data_result = $db->query("SELECT * FROM `$table`");
                if ($data_result && $data_result->num_rows > 0) {
                    $backup_content .= "INSERT INTO `$table` VALUES\n";
                    $rows = [];
                    
                    while ($row = $data_result->fetch_array(MYSQLI_NUM)) {
                        $escaped_row = array_map(function($value) use ($db) {
                            return $value === null ? 'NULL' : "'" . $db->real_escape_string($value) . "'";
                        }, $row);
                        $rows[] = '(' . implode(',', $escaped_row) . ')';
                    }
                    
                    $backup_content .= implode(",\n", $rows) . ";\n\n";
                }
            }
            
            if (file_put_contents($backup_filename, $backup_content)) {
                $file_size = filesize($backup_filename);
                $file_size_mb = round($file_size / 1024 / 1024, 2);
                
                output("✓ Backup alternativo completado!", 'success');
                output("Archivo: $backup_filename", 'success');
                output("Tamaño: {$file_size_mb} MB", 'success');
            } else {
                output("✗ No se pudo crear el archivo de backup", 'error');
            }
        }
    }
    
} catch (Exception $e) {
    output("Error crítico: " . $e->getMessage(), 'error');
}

if (!$is_cli) {
    ?>
        </div>
    </body>
    </html>
    <?php
}
?>
