<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ABLA - Setup</title>

    <!-- Google Fonts - Titillium Web -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Titillium+Web:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['"Titillium Web"', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Phosphor Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <style>
        :root {
            /* Primary colors */
            --primary: #F7931A;
            --primary-hover: #FF9500;
            --primary-light: #FEF4E8;

            /* Background colors */
            --dark-bg: #090C14;
            --card-bg: #13161F;
            --overlay-bg: #00000099;

            /* Text colors */
            --light-text: #FFFFFF;
            --muted-text: rgba(255, 255, 255, 0.7);

            /* Border colors */
            --border-color: rgba(255, 255, 255, 0.1);

            /* Status colors */
            --success: #22c55e;
            --error: #ef4444;
        }

        body {
            background-color: var(--dark-bg);
            color: var(--light-text);
        }

        .step-active {
            background-color: var(--primary);
            color: white;
        }
        .step-completed {
            background-color: var(--success);
            color: white;
        }
        .step-pending {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            text-decoration: none;
            cursor: pointer;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="font-sans" style="background-color: var(--dark-bg); color: var(--light-text);">
    <div class="min-h-screen flex flex-col">
        <header class="py-6" style="background-color: var(--card-bg); color: var(--light-text);">
            <div class="container mx-auto px-4">
                <div class="flex items-center gap-3">
                    <i class="ph ph-bitcoin-circle text-orange-500 text-4xl"></i>
                    <div>
                        <h1 class="text-3xl font-bold leading-tight">ABLA</h1>
                        <p class="text-gray-400 flex items-center gap-1">
                            <i class="ph ph-gear text-xs"></i>
                            Asistente de instalación
                        </p>
                    </div>
                </div>
            </div>
        </header>

        <main class="flex-grow container mx-auto px-4 py-8">
            <!-- Setup Progress -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div class="w-full flex items-center">
                        <?php for ($i = 1; $i <= 4; $i++): ?>
                            <?php
                                $step_class = 'step-pending';
                                if ($i < $current_step) {
                                    $step_class = 'step-completed';
                                } elseif ($i === $current_step) {
                                    $step_class = 'step-active';
                                }
                            ?>
                            <div class="relative flex items-center justify-center">
                                <div class="<?php echo $step_class; ?> rounded-full h-10 w-10 flex items-center justify-center z-10">
                                    <?php echo $i; ?>
                                </div>
                                <?php if ($i < 4): ?>
                                    <div class="h-1 w-full bg-gray-300 absolute right-0" style="left: 50%;">
                                        <?php if ($i < $current_step): ?>
                                            <div class="h-1 bg-green-500" style="width: 100%;"></div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php if ($i < 4): ?>
                                <div class="flex-grow h-1 bg-gray-300 relative">
                                    <?php if ($i < $current_step): ?>
                                        <div class="h-1 bg-green-500 absolute top-0 left-0 right-0"></div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endfor; ?>
                    </div>
                </div>
                <div class="flex justify-between mt-2">
                    <div class="text-center text-sm">Bienvenida</div>
                    <div class="text-center text-sm">Base de datos</div>
                    <div class="text-center text-sm">Administrador</div>
                    <div class="text-center text-sm">Completado</div>
                </div>
            </div>

            <?php if (isset($error)): ?>
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                    <p><?php echo $error; ?></p>
                </div>
            <?php endif; ?>
