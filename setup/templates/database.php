<div class="card p-8" style="background-color: var(--card-bg); color: var(--light-text);">
    <div class="flex items-center gap-3 mb-6">
        <i class="ph ph-database text-orange-500 text-3xl"></i>
        <h2 class="text-2xl font-bold">Configuración de la base de datos</h2>
    </div>

    <div class="mb-6">
        <div style="background-color: rgba(247, 147, 26, 0.1); border: 1px solid rgba(247, 147, 26, 0.2);" class="rounded-lg p-4 mb-6">
            <div class="flex items-start gap-3">
                <i class="ph ph-info text-orange-500 text-xl mt-0.5"></i>
                <p class="text-sm" style="color: var(--light-text);">Ingresa la información de conexión a tu base de datos MySQL. Esta información es necesaria para que ABLA pueda almacenar y recuperar datos.</p>
            </div>
        </div>
    </div>

    <form method="POST" action="/setup/?step=2" class="space-y-5">
        <div class="relative">
            <label for="db_host" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-server text-orange-400"></i>
                Servidor de la base de datos
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-globe"></i>
                </div>
                <input
                    type="text"
                    id="db_host"
                    name="db_host"
                    value="localhost"
                    required
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
            <p class="text-xs mt-1 flex items-center gap-1" style="color: var(--muted-text);">
                <i class="ph ph-info text-orange-400"></i>
                Generalmente es "localhost"
            </p>
        </div>

        <div class="relative">
            <label for="db_username" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-user text-orange-400"></i>
                Nombre de usuario
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-identification-badge"></i>
                </div>
                <input
                    type="text"
                    id="db_username"
                    name="db_username"
                    value="root"
                    required
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
        </div>

        <div class="relative">
            <label for="db_password" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-password text-orange-400"></i>
                Contraseña
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-lock"></i>
                </div>
                <input
                    type="password"
                    id="db_password"
                    name="db_password"
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
        </div>

        <div class="relative">
            <label for="db_name" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-table text-orange-400"></i>
                Nombre de la base de datos
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-database"></i>
                </div>
                <input
                    type="text"
                    id="db_name"
                    name="db_name"
                    value="ablat"
                    required
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
            <p class="text-xs mt-1 flex items-center gap-1" style="color: var(--muted-text);">
                <i class="ph ph-info text-orange-400"></i>
                Si la base de datos no existe, intentaremos crearla automáticamente
            </p>
        </div>

        <div class="flex justify-between pt-6 mt-2">
            <a href="/setup/?step=1" class="btn btn-secondary flex items-center gap-2" style="background-color: rgba(255, 255, 255, 0.1); color: var(--light-text);">
                <i class="ph ph-arrow-left"></i>
                Atrás
            </a>
            <button
                type="submit"
                name="db_submit"
                class="btn btn-primary flex items-center gap-2"
                style="background-color: var(--primary); color: white;"
            >
                Continuar
                <i class="ph ph-arrow-right"></i>
            </button>
        </div>
    </form>
</div>
