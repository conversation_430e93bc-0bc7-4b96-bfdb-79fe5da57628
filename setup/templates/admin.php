<div class="card p-8" style="background-color: var(--card-bg); color: var(--light-text);">
    <div class="flex items-center gap-3 mb-6">
        <i class="ph ph-user-circle-gear text-orange-500 text-3xl"></i>
        <h2 class="text-2xl font-bold">Configuración del administrador</h2>
    </div>

    <div class="mb-6">
        <div style="background-color: rgba(247, 147, 26, 0.1); border: 1px solid rgba(247, 147, 26, 0.2);" class="rounded-lg p-4 mb-6">
            <div class="flex items-start gap-3">
                <i class="ph ph-shield text-orange-500 text-xl mt-0.5"></i>
                <p class="text-sm" style="color: var(--light-text);">Crea una cuenta de administrador para gestionar tu sitio web. Esta cuenta tendrá acceso completo al panel de administración.</p>
            </div>
        </div>
    </div>

    <form method="POST" action="/setup/?step=3" class="space-y-5">
        <div class="relative">
            <label for="username" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-user text-orange-400"></i>
                Nombre de usuario
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-user-circle"></i>
                </div>
                <input
                    type="text"
                    id="username"
                    name="username"
                    value="admin"
                    required
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
        </div>

        <div class="relative">
            <label for="email" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-envelope text-orange-400"></i>
                Correo electrónico
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-at"></i>
                </div>
                <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="<EMAIL>"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
            </div>
        </div>

        <div class="relative">
            <label for="password" class="flex items-center gap-2 text-sm font-medium mb-1" style="color: var(--light-text);">
                <i class="ph ph-password text-orange-400"></i>
                Contraseña
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none" style="color: var(--primary);">
                    <i class="ph ph-lock"></i>
                </div>
                <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    minlength="8"
                    class="w-full pl-10 p-2.5 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    style="background-color: rgba(255, 255, 255, 0.1); border: 1px solid var(--border-color); color: var(--light-text);"
                >
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePasswordVisibility()" style="color: var(--muted-text);">
                    <i class="ph ph-eye" id="password-toggle-icon"></i>
                </div>
            </div>
            <p class="text-xs mt-1 flex items-center gap-1" style="color: var(--muted-text);">
                <i class="ph ph-info text-orange-400"></i>
                La contraseña debe tener al menos 8 caracteres
            </p>
        </div>

        <div class="flex justify-between pt-6 mt-2">
            <a href="/setup/?step=2" class="btn btn-secondary flex items-center gap-2" style="background-color: rgba(255, 255, 255, 0.1); color: var(--light-text);">
                <i class="ph ph-arrow-left"></i>
                Atrás
            </a>
            <button
                type="submit"
                name="admin_submit"
                class="btn btn-primary flex items-center gap-2"
                style="background-color: var(--primary); color: white;"
            >
                Continuar
                <i class="ph ph-arrow-right"></i>
            </button>
        </div>
    </form>
</div>

<script>
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const passwordToggleIcon = document.getElementById('password-toggle-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            passwordToggleIcon.classList.remove('ph-eye');
            passwordToggleIcon.classList.add('ph-eye-slash');
        } else {
            passwordInput.type = 'password';
            passwordToggleIcon.classList.remove('ph-eye-slash');
            passwordToggleIcon.classList.add('ph-eye');
        }
    }
</script>
