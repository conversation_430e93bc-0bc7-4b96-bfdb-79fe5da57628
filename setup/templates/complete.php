<div class="card p-8" style="background-color: var(--card-bg); color: var(--light-text);">
    <div class="text-center mb-8">
        <i class="ph ph-check-circle text-green-500 text-7xl"></i>
        <h2 class="text-2xl font-bold mt-4">¡Instalación completada!</h2>
        <p class="mt-2" style="color: var(--muted-text);">Todo está listo para comenzar</p>
    </div>

    <div class="mb-8">
        <div class="alert alert-success mb-6">
            <i class="ph ph-check-circle text-xl"></i>
            <div>
                <p class="font-semibold">ABLA ha sido instalado correctamente</p>
                <p class="text-sm">La base de datos y el usuario administrador han sido configurados con éxito.</p>
            </div>
        </div>

        <div style="background-color: rgba(247, 147, 26, 0.1); border: 1px solid rgba(247, 147, 26, 0.2);" class="rounded-lg p-5 mb-6">
            <h3 class="font-bold mb-3 flex items-center gap-2" style="color: var(--light-text);">
                <i class="ph ph-list-checks text-orange-500"></i>
                Próximos pasos:
            </h3>
            <ul class="space-y-3 ml-2">
                <li class="flex items-start gap-2">
                    <i class="ph ph-caret-right text-orange-500 mt-1"></i>
                    <span>Personaliza el contenido de tu sitio web desde el panel de administración.</span>
                </li>
                <li class="flex items-start gap-2">
                    <i class="ph ph-caret-right text-orange-500 mt-1"></i>
                    <span>Agrega iniciativas y eventos para mostrar en tu sitio.</span>
                </li>
                <li class="flex items-start gap-2">
                    <i class="ph ph-caret-right text-orange-500 mt-1"></i>
                    <span>Configura las redes sociales y la información de contacto.</span>
                </li>
            </ul>
        </div>
    </div>

    <div class="flex flex-col sm:flex-row justify-center gap-4">
        <a href="/" class="btn btn-secondary flex items-center justify-center gap-2" style="background-color: rgba(255, 255, 255, 0.1); color: var(--light-text);">
            <i class="ph ph-globe"></i>
            Ver sitio web
        </a>
        <a href="/?route=admin" class="btn btn-primary flex items-center justify-center gap-2" style="background-color: var(--primary); color: white;">
            <i class="ph ph-user-gear"></i>
            Ir al panel de administración
        </a>
    </div>

    <div class="mt-8 pt-4" style="border-top: 1px solid var(--border-color);">
        <div class="flex items-start gap-2 text-sm" style="color: var(--muted-text);">
            <i class="ph ph-warning-circle text-amber-500 mt-0.5"></i>
            <p>Por seguridad, el directorio "setup" ha sido protegido automáticamente. Considera eliminarlo si no lo necesitarás en el futuro.</p>
        </div>
    </div>
</div>

<?php
// Create an .htaccess file to protect the setup directory
$htaccess_content = "# Protect setup directory after installation\n";
$htaccess_content .= "Order Allow,Deny\n";
$htaccess_content .= "Deny from all\n";

// Make sure the directory exists before writing to it
$setup_dir = __DIR__ . '/../setup';
if (!is_dir($setup_dir)) {
    // Create the directory if it doesn't exist
    mkdir($setup_dir, 0755, true);
}

// Now write the .htaccess file
try {
    file_put_contents($setup_dir . '/.htaccess', $htaccess_content);
} catch (Exception $e) {
    // Silently fail - not critical
}
?>
