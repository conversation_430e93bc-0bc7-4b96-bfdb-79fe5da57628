<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database functions
require_once __DIR__ . '/../includes/db.php';

// Define setup steps
$steps = [
    1 => 'welcome',
    2 => 'database',
    3 => 'admin',
    4 => 'complete'
];

// Get current step
$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// Validate step
if (!isset($steps[$current_step])) {
    $current_step = 1;
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Database configuration step
    if (isset($_POST['db_submit'])) {
        $db_config = [
            'host' => $_POST['db_host'] ?? 'localhost',
            'username' => $_POST['db_username'] ?? '',
            'password' => $_POST['db_password'] ?? '',
            'database' => $_POST['db_name'] ?? 'ablat'
        ];
        
        // Test connection
        $test_result = testConnection($db_config);
        
        if ($test_result['success']) {
            // Check if database exists
            $db_exists = databaseExists($db_config);
            
            if ($db_exists['success']) {
                // Save config to session for next step
                $_SESSION['db_config'] = $db_config;
                
                // If database doesn't exist, create it
                if (!$db_exists['exists']) {
                    $setup_result = setupDatabase($db_config);
                    
                    if ($setup_result['success']) {
                        // Save config to file
                        $config_content = "<?php\n";
                        $config_content .= "// Database configuration\n";
                        $config_content .= "\$GLOBALS['db_config'] = [\n";
                        $config_content .= "    'host' => '" . addslashes($db_config['host']) . "',\n";
                        $config_content .= "    'username' => '" . addslashes($db_config['username']) . "',\n";
                        $config_content .= "    'password' => '" . addslashes($db_config['password']) . "',\n";
                        $config_content .= "    'database' => '" . addslashes($db_config['database']) . "'\n";
                        $config_content .= "];\n";
                        
                        file_put_contents(__DIR__ . '/../config.php', $config_content);
                        
                        // Redirect to next step
                        header('Location: /setup/?step=3');
                        exit;
                    } else {
                        $error = "Error creating database: " . $setup_result['error'];
                    }
                } else {
                    // Database exists, save config to file
                    $config_content = "<?php\n";
                    $config_content .= "// Database configuration\n";
                    $config_content .= "\$GLOBALS['db_config'] = [\n";
                    $config_content .= "    'host' => '" . addslashes($db_config['host']) . "',\n";
                    $config_content .= "    'username' => '" . addslashes($db_config['username']) . "',\n";
                    $config_content .= "    'password' => '" . addslashes($db_config['password']) . "',\n";
                    $config_content .= "    'database' => '" . addslashes($db_config['database']) . "'\n";
                    $config_content .= "];\n";
                    
                    file_put_contents(__DIR__ . '/../config.php', $config_content);
                    
                    // Redirect to next step
                    header('Location: /setup/?step=3');
                    exit;
                }
            } else {
                $error = "Error checking database: " . $db_exists['error'];
            }
        } else {
            $error = "Connection failed: " . $test_result['error'];
        }
    }
    
    // Admin user creation step
    if (isset($_POST['admin_submit'])) {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        $email = $_POST['email'] ?? '';
        
        if (empty($username) || empty($password) || empty($email)) {
            $error = "All fields are required.";
        } elseif (strlen($password) < 8) {
            $error = "Password must be at least 8 characters long.";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Please enter a valid email address.";
        } else {
            // Get database connection
            $db = connectDB();
            
            if (!is_array($db)) {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Check if users table exists
                $result = $db->query("SHOW TABLES LIKE 'users'");
                
                if ($result && $result->num_rows > 0) {
                    // Check if admin user exists
                    $stmt = $db->prepare("SELECT id FROM users WHERE username = 'admin'");
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $result->num_rows > 0) {
                        // Update existing admin user
                        $stmt = $db->prepare("UPDATE users SET username = ?, password = ?, email = ? WHERE username = 'admin'");
                        $stmt->bind_param("sss", $username, $hashed_password, $email);
                    } else {
                        // Insert new admin user
                        $stmt = $db->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
                        $stmt->bind_param("sss", $username, $hashed_password, $email);
                    }
                    
                    if ($stmt->execute()) {
                        // Redirect to next step
                        header('Location: /setup/?step=4');
                        exit;
                    } else {
                        $error = "Error creating admin user: " . $db->error;
                    }
                } else {
                    // Create users table
                    $db->query("
                        CREATE TABLE IF NOT EXISTS users (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            username VARCHAR(50) NOT NULL UNIQUE,
                            password VARCHAR(255) NOT NULL,
                            email VARCHAR(100) NOT NULL UNIQUE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    ");
                    
                    // Insert admin user
                    $stmt = $db->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
                    $stmt->bind_param("sss", $username, $hashed_password, $email);
                    
                    if ($stmt->execute()) {
                        // Redirect to next step
                        header('Location: /setup/?step=4');
                        exit;
                    } else {
                        $error = "Error creating admin user: " . $db->error;
                    }
                }
            } else {
                $error = "Database connection failed: " . $db['error'];
            }
        }
    }
}

// Include the appropriate step template
include __DIR__ . '/templates/header.php';
include __DIR__ . '/templates/' . $steps[$current_step] . '.php';
include __DIR__ . '/templates/footer.php';
?>
