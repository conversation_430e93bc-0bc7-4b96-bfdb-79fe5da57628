<?php
session_start();

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = [
        'success' => false,
        'message' => ''
    ];

    // Check honeypot field - if it's filled, it's likely a bot
    if (isset($_POST['website']) && !empty($_POST['website'])) {
        // Pretend success but don't process the form
        $response['success'] = true;
        $response['message'] = '¡Mensaje enviado con éxito! Nos pondremos en contacto contigo pronto.';
        echo json_encode($response);
        exit;
    }

    // Verify CAPTCHA
    if (!isset($_POST['captcha']) || !isset($_SESSION['captcha']) || strtoupper($_POST['captcha']) !== $_SESSION['captcha']) {
        $response['message'] = 'El código de verificación es incorrecto. Por favor, inténtalo de nuevo.';
        echo json_encode($response);
        exit;
    }

    // Validate required fields
    $required_fields = ['name', 'email', 'subject', 'message'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $response['message'] = 'Por favor, completa todos los campos obligatorios.';
            echo json_encode($response);
            exit;
        }
    }

    // Validate email
    if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Por favor, ingresa una dirección de correo electrónico válida.';
        echo json_encode($response);
        exit;
    }

    // Get form data
    $name = htmlspecialchars($_POST['name']);
    $email = htmlspecialchars($_POST['email']);
    $subject = htmlspecialchars($_POST['subject']);
    $message = htmlspecialchars($_POST['message']);
    $instagram = isset($_POST['instagram']) ? htmlspecialchars($_POST['instagram']) : '';
    $twitter = isset($_POST['twitter']) ? htmlspecialchars($_POST['twitter']) : '';

    // Prepare email content
    $to = '<EMAIL>'; // Replace with your email
    $email_subject = "Nuevo mensaje de contacto: $subject";

    $email_body = "Has recibido un nuevo mensaje desde el formulario de contacto.\n\n";
    $email_body .= "Detalles:\n";
    $email_body .= "Nombre: $name\n";
    $email_body .= "Email: $email\n";

    if (!empty($instagram)) {
        $email_body .= "Instagram: @$instagram\n";
    }

    if (!empty($twitter)) {
        $email_body .= "Twitter/X: @$twitter\n";
    }

    $email_body .= "Asunto: $subject\n";
    $email_body .= "Mensaje:\n$message\n";

    // Email headers
    $headers = "From: $email\r\n";
    $headers .= "Reply-To: $email\r\n";

    // Save to database first
    require_once 'includes/db.php';
    $db = connectDB();
    $database_saved = false;

    if (!is_array($db)) {
        // Get client information
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Save to database
        $stmt = $db->prepare("INSERT INTO messages (name, email, subject, message, instagram, twitter, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("ssssssss", $name, $email, $subject, $message, $instagram, $twitter, $ip_address, $user_agent);
            if ($stmt->execute()) {
                $database_saved = true;
            }
            $stmt->close();
        }
    }

    // Send email
    $email_sent = mail($to, $email_subject, $email_body, $headers);

    // Determine response based on database and email status
    if ($database_saved) {
        // Message saved to database - this is the primary success condition
        $response['success'] = true;
        if ($email_sent) {
            $response['message'] = '¡Mensaje enviado con éxito! Nos pondremos en contacto contigo pronto.';
        } else {
            $response['message'] = '¡Mensaje recibido con éxito! Nos pondremos en contacto contigo pronto.';
        }
    } else {
        // Database save failed, check email
        if ($email_sent) {
            $response['success'] = true;
            $response['message'] = '¡Mensaje enviado con éxito! Nos pondremos en contacto contigo pronto.';
        } else {
            $response['message'] = 'Lo sentimos, hubo un problema al procesar tu mensaje. Por favor, inténtalo de nuevo más tarde.';
        }
    }

    // Generate new CAPTCHA for next submission
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $captcha = '';
    for ($i = 0; $i < 6; $i++) {
        $captcha .= $chars[rand(0, strlen($chars) - 1)];
    }
    $_SESSION['captcha'] = $captcha;
    $response['new_captcha'] = $captcha;

    // Return JSON response
    echo json_encode($response);
    exit;
}

// If not a POST request, redirect to home
header('Location: /');
exit;
