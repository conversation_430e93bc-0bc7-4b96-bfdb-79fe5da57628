<?php
/**
 * Avatar Fetcher Utility
 * 
 * This utility provides functions to fetch profile images from social media platforms.
 * Due to API restrictions, we'll use alternative methods and fallbacks.
 */

/**
 * Get Instagram profile picture URL
 * Note: Instagram has strict API policies, so this uses a fallback approach
 */
function getInstagramAvatar($username) {
    // Remove @ symbol if present
    $username = ltrim($username, '@');
    
    // Try multiple methods to get Instagram avatar
    $methods = [
        'method1' => "https://www.instagram.com/$username/",
        'method2' => "https://i.instagram.com/api/v1/users/web_profile_info/?username=$username"
    ];
    
    foreach ($methods as $method_name => $url) {
        try {
            $avatar_url = fetchInstagramAvatarMethod($url, $username, $method_name);
            if ($avatar_url) {
                return [
                    'success' => true,
                    'avatar_url' => $avatar_url,
                    'method' => $method_name,
                    'source' => 'instagram'
                ];
            }
        } catch (Exception $e) {
            // Continue to next method
            continue;
        }
    }
    
    // Fallback to a default Instagram-style avatar
    return [
        'success' => false,
        'avatar_url' => getDefaultAvatar($username, 'instagram'),
        'method' => 'fallback',
        'source' => 'instagram'
    ];
}

/**
 * Get X (Twitter) profile picture URL
 */
function getTwitterAvatar($username) {
    // Remove @ symbol if present
    $username = ltrim($username, '@');
    
    // Try to get Twitter avatar using web scraping approach
    try {
        $url = "https://twitter.com/$username";
        $avatar_url = fetchTwitterAvatarMethod($url, $username);
        
        if ($avatar_url) {
            return [
                'success' => true,
                'avatar_url' => $avatar_url,
                'method' => 'web_scraping',
                'source' => 'twitter'
            ];
        }
    } catch (Exception $e) {
        // Fallback
    }
    
    // Fallback to default Twitter-style avatar
    return [
        'success' => false,
        'avatar_url' => getDefaultAvatar($username, 'twitter'),
        'method' => 'fallback',
        'source' => 'twitter'
    ];
}

/**
 * Fetch Instagram avatar using web scraping
 */
function fetchInstagramAvatarMethod($url, $username, $method) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive',
            ],
            'timeout' => 10
        ]
    ]);
    
    $html = @file_get_contents($url, false, $context);
    
    if (!$html) {
        return false;
    }
    
    // Look for profile image in meta tags
    $patterns = [
        '/<meta property="og:image" content="([^"]+)"/',
        '/<meta name="twitter:image" content="([^"]+)"/',
        '/<img[^>]+class="[^"]*profile[^"]*"[^>]+src="([^"]+)"/',
        '/<img[^>]+src="([^"]+)"[^>]+class="[^"]*profile[^"]*"/',
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];
            
            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) && 
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }
    
    return false;
}

/**
 * Fetch Twitter avatar using web scraping
 */
function fetchTwitterAvatarMethod($url, $username) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            ],
            'timeout' => 10
        ]
    ]);
    
    $html = @file_get_contents($url, false, $context);
    
    if (!$html) {
        return false;
    }
    
    // Look for profile image in various places
    $patterns = [
        '/<img[^>]+data-testid="[^"]*avatar[^"]*"[^>]+src="([^"]+)"/',
        '/<img[^>]+src="([^"]+)"[^>]+data-testid="[^"]*avatar[^"]*"/',
        '/<meta property="og:image" content="([^"]+)"/',
        '/<meta name="twitter:image" content="([^"]+)"/',
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];
            
            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) && 
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }
    
    return false;
}

/**
 * Generate a default avatar based on username and platform
 */
function getDefaultAvatar($username, $platform = 'instagram') {
    // Create a deterministic color based on username
    $hash = md5($username);
    $hue = hexdec(substr($hash, 0, 2)) / 255 * 360;
    
    // Platform-specific styling
    $colors = [
        'instagram' => [
            'bg' => "hsl($hue, 70%, 50%)",
            'text' => '#ffffff'
        ],
        'twitter' => [
            'bg' => "hsl($hue, 60%, 45%)",
            'text' => '#ffffff'
        ]
    ];
    
    $color = $colors[$platform] ?? $colors['instagram'];
    
    // Get initials
    $initials = strtoupper(substr($username, 0, 2));
    
    // Generate SVG avatar
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
    <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
        <circle cx="75" cy="75" r="75" fill="' . $color['bg'] . '"/>
        <text x="75" y="85" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
              text-anchor="middle" fill="' . $color['text'] . '">' . $initials . '</text>
    </svg>';
    
    // Convert to data URL
    return 'data:image/svg+xml;base64,' . base64_encode($svg);
}

/**
 * Update initiative avatar in database
 */
function updateInitiativeAvatar($db, $initiative_id, $avatar_url, $source) {
    $stmt = $db->prepare("UPDATE initiatives SET avatar_url = ?, avatar_source = ? WHERE id = ?");
    $stmt->bind_param("ssi", $avatar_url, $source, $initiative_id);
    
    if ($stmt->execute()) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

/**
 * Fetch and update avatar for an initiative
 */
function fetchAndUpdateAvatar($db, $initiative_id, $instagram_user = null, $twitter_user = null) {
    $results = [];
    
    // Try Instagram first if available
    if ($instagram_user) {
        $instagram_result = getInstagramAvatar($instagram_user);
        $results['instagram'] = $instagram_result;
        
        if ($instagram_result['success']) {
            if (updateInitiativeAvatar($db, $initiative_id, $instagram_result['avatar_url'], 'instagram')) {
                return [
                    'success' => true,
                    'avatar_url' => $instagram_result['avatar_url'],
                    'source' => 'instagram',
                    'method' => $instagram_result['method']
                ];
            }
        }
    }
    
    // Try Twitter if Instagram failed and Twitter is available
    if ($twitter_user) {
        $twitter_result = getTwitterAvatar($twitter_user);
        $results['twitter'] = $twitter_result;
        
        if ($twitter_result['success']) {
            if (updateInitiativeAvatar($db, $initiative_id, $twitter_result['avatar_url'], 'twitter')) {
                return [
                    'success' => true,
                    'avatar_url' => $twitter_result['avatar_url'],
                    'source' => 'twitter',
                    'method' => $twitter_result['method']
                ];
            }
        }
    }
    
    // Use fallback avatar
    $fallback_user = $instagram_user ?: $twitter_user ?: 'unknown';
    $fallback_source = $instagram_user ? 'instagram' : 'twitter';
    $fallback_avatar = getDefaultAvatar($fallback_user, $fallback_source);
    
    if (updateInitiativeAvatar($db, $initiative_id, $fallback_avatar, $fallback_source)) {
        return [
            'success' => false,
            'avatar_url' => $fallback_avatar,
            'source' => $fallback_source,
            'method' => 'fallback',
            'attempts' => $results
        ];
    }
    
    return [
        'success' => false,
        'error' => 'Failed to update avatar in database',
        'attempts' => $results
    ];
}

/**
 * Get avatar URL for display (with fallback)
 */
function getAvatarForDisplay($initiative) {
    // If we have a stored avatar, use it
    if (!empty($initiative['avatar_url'])) {
        return $initiative['avatar_url'];
    }
    
    // Generate fallback based on available social media
    $username = $initiative['instagram_user'] ?? $initiative['twitter_user'] ?? $initiative['name'];
    $platform = !empty($initiative['instagram_user']) ? 'instagram' : 'twitter';
    
    return getDefaultAvatar($username, $platform);
}
?>
