<?php
// Database connection
function connectDB($config = null) {
    // Use provided config or get from config file
    if ($config === null) {
        // Check if we're using the new config format with constants
        if (defined('DB_HOST') && defined('DB_USER') && defined('DB_PASSWORD') && defined('DB_NAME')) {
            $db_config = [
                'host' => DB_HOST,
                'username' => DB_USER,
                'password' => DB_PASSWORD,
                'database' => DB_NAME,
                'charset' => defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4'
            ];
        }
        // Check if config file exists with old format
        else if (file_exists(__DIR__ . '/../config.php')) {
            require_once __DIR__ . '/../config.php';
            if (isset($GLOBALS['db_config'])) {
                $db_config = $GLOBALS['db_config'];
            } else {
                // Default config if no valid config found
                $db_config = [
                    'host' => 'localhost',
                    'username' => 'root',
                    'password' => '',
                    'database' => 'abla_db'
                ];
            }
        } else {
            // Default config if no config file exists
            $db_config = [
                'host' => 'localhost',
                'username' => 'root',
                'password' => '',
                'database' => 'abla_db'
            ];
        }
    } else {
        $db_config = $config;
    }

    // Try to connect to the database
    try {
        // Add error suppression to prevent warnings from being displayed
        $conn = @new mysqli(
            $db_config['host'],
            $db_config['username'],
            $db_config['password'],
            $db_config['database']
        );

        // Check for connection error
        if ($conn->connect_error) {
            // Special handling for "No such file or directory" error
            if (strpos($conn->connect_error, 'No such file or directory') !== false ||
                strpos($conn->connect_error, 'Connection refused') !== false) {
                return [
                    'success' => false,
                    'error' => 'No se pudo conectar al servidor MySQL. Asegúrate de que MySQL esté instalado y en ejecución.',
                    'error_details' => $conn->connect_error,
                    'error_code' => $conn->connect_errno
                ];
            }

            return [
                'success' => false,
                'error' => $conn->connect_error,
                'error_code' => $conn->connect_errno
            ];
        }

        // Set charset if defined
        if (isset($db_config['charset'])) {
            $conn->set_charset($db_config['charset']);
        } else {
            $conn->set_charset('utf8mb4');
        }

        return $conn;
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => $e->getCode()
        ];
    }
}

// Function to check if database exists
function databaseExists($config) {
    try {
        // Try to connect to the server without specifying a database
        $temp_config = $config;
        $temp_config['database'] = '';

        $conn = new mysqli(
            $temp_config['host'],
            $temp_config['username'],
            $temp_config['password']
        );

        if ($conn->connect_error) {
            return [
                'success' => false,
                'error' => $conn->connect_error
            ];
        }

        // Check if database exists
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$config['database']}'");
        $exists = $result && $result->num_rows > 0;

        $conn->close();

        return [
            'success' => true,
            'exists' => $exists
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to check if a table exists
function tableExists($conn, $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    return ($result && $result->num_rows > 0);
}

// Function to create database and tables
function setupDatabase($config) {
    try {
        // Connect to server without database
        $temp_config = $config;
        $temp_config['database'] = '';

        $conn = new mysqli(
            $temp_config['host'],
            $temp_config['username'],
            $temp_config['password']
        );

        if ($conn->connect_error) {
            return [
                'success' => false,
                'error' => $conn->connect_error
            ];
        }

        // Create database
        $conn->query("CREATE DATABASE IF NOT EXISTS `{$config['database']}`");

        // Select the database
        $conn->select_db($config['database']);

        // Read SQL file
        $sql = file_get_contents(__DIR__ . '/../database_setup.sql');

        // Split SQL file into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));

        // Execute each statement
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $result = $conn->query($statement);
                if (!$result) {
                    return [
                        'success' => false,
                        'error' => $conn->error,
                        'statement' => $statement
                    ];
                }
            }
        }

        $conn->close();

        return [
            'success' => true
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Function to initialize database with sample data
function initializeDatabase($conn) {
    $tables = [
        'content' => "CREATE TABLE IF NOT EXISTS `content` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `section` VARCHAR(50) NOT NULL,
            `title` VARCHAR(255) NOT NULL,
            `subtitle` VARCHAR(255),
            `content` TEXT,
            `image_url` VARCHAR(255),
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        'initiatives' => "CREATE TABLE IF NOT EXISTS `initiatives` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `name` VARCHAR(255) NOT NULL,
            `instagram_user` VARCHAR(100) NOT NULL,
            `instagram_link` VARCHAR(255),
            `description` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        'events' => "CREATE TABLE IF NOT EXISTS `events` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `title` VARCHAR(255) NOT NULL,
            `date` DATE,
            `description` TEXT,
            `location` VARCHAR(255),
            `image_url` VARCHAR(255),
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        'contacts' => "CREATE TABLE IF NOT EXISTS `contacts` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `name` VARCHAR(255) NOT NULL,
            `email` VARCHAR(255) NOT NULL,
            `message` TEXT NOT NULL,
            `interest` VARCHAR(50),
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        'users' => "CREATE TABLE IF NOT EXISTS `users` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `username` VARCHAR(50) NOT NULL UNIQUE,
            `password` VARCHAR(255) NOT NULL,
            `email` VARCHAR(100) NOT NULL UNIQUE,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        'themes' => "CREATE TABLE IF NOT EXISTS `themes` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `name` VARCHAR(100) NOT NULL,
            `is_active` BOOLEAN DEFAULT FALSE,
            `primary_color` VARCHAR(20) NOT NULL,
            `primary_hover` VARCHAR(20) NOT NULL,
            `primary_light` VARCHAR(20) NOT NULL,
            `dark_bg` VARCHAR(20) NOT NULL,
            `card_bg` VARCHAR(20) NOT NULL,
            `overlay_bg` VARCHAR(20) NOT NULL,
            `light_text` VARCHAR(20) NOT NULL,
            `muted_text` VARCHAR(20) NOT NULL,
            `border_color` VARCHAR(20) NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        'messages' => "CREATE TABLE IF NOT EXISTS `messages` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `name` VARCHAR(255) NOT NULL,
            `email` VARCHAR(255) NOT NULL,
            `subject` VARCHAR(255) NOT NULL,
            `message` TEXT NOT NULL,
            `instagram` VARCHAR(100),
            `twitter` VARCHAR(100),
            `status` ENUM('unread', 'read', 'replied') DEFAULT 'unread',
            `ip_address` VARCHAR(45),
            `user_agent` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )"
    ];

    $created_tables = [];
    $errors = [];

    // Create tables
    foreach ($tables as $table_name => $query) {
        try {
            // Check if table exists before creating
            $table_existed = tableExists($conn, $table_name);

            $result = $conn->query($query);
            if ($result) {
                if (!$table_existed) {
                    $created_tables[] = $table_name;
                }
            } else {
                $errors[] = "Error creating table $table_name: " . $conn->error;
            }
        } catch (Exception $e) {
            $errors[] = "Error creating table $table_name: " . $e->getMessage();
        }
    }

    // Add sample data
    $sample_data_added = [];

    // Add hero content
    try {
        if (!tableExists($conn, 'content') || $conn->query("SELECT COUNT(*) as count FROM content WHERE section = 'hero'")->fetch_assoc()['count'] == 0) {
            $stmt = $conn->prepare("INSERT INTO content (section, title, content) VALUES (?, ?, ?)");
            $section = "hero";
            $title = "ASOCIACIÓN <span class=\"text-orange-500\">BITCOIN</span> LATINOAMÉRICA";
            $content = "Promoviendo la adopción de Bitcoin en América Latina a través de educación, desarrollo y colaboración entre comunidades.";
            $stmt->bind_param("sss", $section, $title, $content);
            if ($stmt->execute()) {
                $sample_data_added[] = "Hero content";
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        $errors[] = "Error adding hero content: " . $e->getMessage();
    }

    // Add sample initiatives
    try {
        if (!tableExists($conn, 'initiatives') || $conn->query("SELECT COUNT(*) as count FROM initiatives")->fetch_assoc()['count'] == 0) {
            $initiatives = [
                [
                    'name' => 'Bitcoin Beach',
                    'instagram_user' => '@bitcoinbeach',
                    'instagram_link' => 'https://www.instagram.com/bitcoinbeach/',
                    'description' => 'Proyecto pionero de adopción de Bitcoin en El Zonte, El Salvador, que inspiró la ley Bitcoin en el país.'
                ],
                [
                    'name' => 'Mi Primer Bitcoin',
                    'instagram_user' => '@myfirstbitcoin.io',
                    'instagram_link' => 'https://www.instagram.com/myfirstbitcoin.io/',
                    'description' => 'Educación Bitcoin gratuita para todos. Pioneros en educación Bitcoin en El Salvador.'
                ],
                [
                    'name' => 'Bitcoin Argentina',
                    'instagram_user' => '@bitcoinargentina',
                    'instagram_link' => 'https://www.instagram.com/bitcoinargentina/',
                    'description' => 'ONG que promueve y desarrolla el ecosistema Bitcoin y blockchain en Argentina desde 2013.'
                ]
            ];

            $stmt = $conn->prepare("INSERT INTO initiatives (name, instagram_user, instagram_link, description) VALUES (?, ?, ?, ?)");
            foreach ($initiatives as $initiative) {
                $stmt->bind_param("ssss", $initiative['name'], $initiative['instagram_user'], $initiative['instagram_link'], $initiative['description']);
                if ($stmt->execute()) {
                    $sample_data_added[] = "Initiative: " . $initiative['name'];
                }
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        $errors[] = "Error adding initiatives: " . $e->getMessage();
    }

    // Add default theme
    try {
        if (!tableExists($conn, 'themes') || $conn->query("SELECT COUNT(*) as count FROM themes")->fetch_assoc()['count'] == 0) {
            $stmt = $conn->prepare("INSERT INTO themes (name, is_active, primary_color, primary_hover, primary_light, dark_bg, card_bg, overlay_bg, light_text, muted_text, border_color) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $theme_name = "Bitcoin Orange";
            $is_active = 1;
            $primary_color = "#F7931A";
            $primary_hover = "#FF9500";
            $primary_light = "#FEF4E8";
            $dark_bg = "#090C14";
            $card_bg = "#13161F";
            $overlay_bg = "#00000099";
            $light_text = "#FFFFFF";
            $muted_text = "rgba(255, 255, 255, 0.7)";
            $border_color = "rgba(255, 255, 255, 0.1)";

            $stmt->bind_param("sisssssssss", $theme_name, $is_active, $primary_color, $primary_hover, $primary_light, $dark_bg, $card_bg, $overlay_bg, $light_text, $muted_text, $border_color);
            if ($stmt->execute()) {
                $sample_data_added[] = "Default theme";
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        $errors[] = "Error adding default theme: " . $e->getMessage();
    }

    // Add admin user
    try {
        if (!tableExists($conn, 'users') || $conn->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'")->fetch_assoc()['count'] == 0) {
            $stmt = $conn->prepare("INSERT INTO users (username, password, email) VALUES (?, ?, ?)");
            $username = "admin";
            $password = password_hash("admin123", PASSWORD_DEFAULT);
            $email = "<EMAIL>";

            $stmt->bind_param("sss", $username, $password, $email);
            if ($stmt->execute()) {
                $sample_data_added[] = "Admin user (username: admin, password: admin123)";
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        $errors[] = "Error adding admin user: " . $e->getMessage();
    }

    return [
        'success' => count($errors) === 0,
        'created_tables' => $created_tables,
        'sample_data_added' => $sample_data_added,
        'errors' => $errors
    ];
}

// Function to test database connection
function testConnection($config) {
    try {
        // Try to connect to the server without specifying a database
        $conn = new mysqli(
            $config['host'],
            $config['username'],
            $config['password']
        );

        if ($conn->connect_error) {
            return [
                'success' => false,
                'error' => $conn->connect_error
            ];
        }

        $conn->close();

        return [
            'success' => true
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}