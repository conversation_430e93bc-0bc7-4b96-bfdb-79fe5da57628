<?php
/**
 * Theme management functions
 */

/**
 * Get the active theme
 * 
 * @return array|null The active theme or null if no active theme
 */
function getActiveTheme() {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return null;
    }
    
    $result = $db->query("SELECT * FROM themes WHERE is_active = TRUE LIMIT 1");
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }
    
    // No active theme found, try to get the first theme
    $result = $db->query("SELECT * FROM themes LIMIT 1");
    
    if ($result && $result->num_rows > 0) {
        $theme = $result->fetch_assoc();
        
        // Set this theme as active
        $db->query("UPDATE themes SET is_active = TRUE WHERE id = {$theme['id']}");
        
        return $theme;
    }
    
    // No themes found, return default theme
    return [
        'id' => 0,
        'name' => 'Default',
        'primary_color' => '#F7931A',
        'primary_hover' => '#FF9500',
        'primary_light' => '#FEF4E8',
        'dark_bg' => '#090C14',
        'card_bg' => '#13161F',
        'overlay_bg' => '#00000099',
        'light_text' => '#FFFFFF',
        'muted_text' => 'rgba(255, 255, 255, 0.7)',
        'border_color' => 'rgba(255, 255, 255, 0.1)'
    ];
}

/**
 * Get all themes
 * 
 * @return array List of themes
 */
function getAllThemes() {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return [];
    }
    
    $themes = [];
    $result = $db->query("SELECT * FROM themes ORDER BY name ASC");
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $themes[] = $row;
        }
    }
    
    return $themes;
}

/**
 * Set a theme as active
 * 
 * @param int $theme_id The theme ID to set as active
 * @return bool True if successful, false otherwise
 */
function setActiveTheme($theme_id) {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return false;
    }
    
    // First, set all themes as inactive
    $db->query("UPDATE themes SET is_active = FALSE");
    
    // Then set the selected theme as active
    $stmt = $db->prepare("UPDATE themes SET is_active = TRUE WHERE id = ?");
    $stmt->bind_param("i", $theme_id);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Create a new theme
 * 
 * @param array $theme_data Theme data
 * @return int|bool The new theme ID if successful, false otherwise
 */
function createTheme($theme_data) {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return false;
    }
    
    $stmt = $db->prepare("
        INSERT INTO themes (
            name, primary_color, primary_hover, primary_light, 
            dark_bg, card_bg, overlay_bg, light_text, muted_text, border_color
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param(
        "ssssssssss",
        $theme_data['name'],
        $theme_data['primary_color'],
        $theme_data['primary_hover'],
        $theme_data['primary_light'],
        $theme_data['dark_bg'],
        $theme_data['card_bg'],
        $theme_data['overlay_bg'],
        $theme_data['light_text'],
        $theme_data['muted_text'],
        $theme_data['border_color']
    );
    
    $result = $stmt->execute();
    $new_id = $result ? $db->insert_id : false;
    $stmt->close();
    
    return $new_id;
}

/**
 * Update an existing theme
 * 
 * @param int $theme_id The theme ID to update
 * @param array $theme_data Theme data
 * @return bool True if successful, false otherwise
 */
function updateTheme($theme_id, $theme_data) {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return false;
    }
    
    $stmt = $db->prepare("
        UPDATE themes SET
            name = ?,
            primary_color = ?,
            primary_hover = ?,
            primary_light = ?,
            dark_bg = ?,
            card_bg = ?,
            overlay_bg = ?,
            light_text = ?,
            muted_text = ?,
            border_color = ?
        WHERE id = ?
    ");
    
    $stmt->bind_param(
        "ssssssssssi",
        $theme_data['name'],
        $theme_data['primary_color'],
        $theme_data['primary_hover'],
        $theme_data['primary_light'],
        $theme_data['dark_bg'],
        $theme_data['card_bg'],
        $theme_data['overlay_bg'],
        $theme_data['light_text'],
        $theme_data['muted_text'],
        $theme_data['border_color'],
        $theme_id
    );
    
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Delete a theme
 * 
 * @param int $theme_id The theme ID to delete
 * @return bool True if successful, false otherwise
 */
function deleteTheme($theme_id) {
    $db = connectDB();
    
    if (is_array($db)) {
        // Connection error
        return false;
    }
    
    // Check if this is the active theme
    $result = $db->query("SELECT is_active FROM themes WHERE id = {$theme_id}");
    $is_active = ($result && $result->num_rows > 0) ? $result->fetch_assoc()['is_active'] : false;
    
    // Delete the theme
    $stmt = $db->prepare("DELETE FROM themes WHERE id = ?");
    $stmt->bind_param("i", $theme_id);
    $result = $stmt->execute();
    $stmt->close();
    
    // If this was the active theme, set another theme as active
    if ($is_active && $result) {
        $db->query("UPDATE themes SET is_active = TRUE ORDER BY id LIMIT 1");
    }
    
    return $result;
}

/**
 * Generate CSS variables for the active theme
 * 
 * @return string CSS variables
 */
function generateThemeCSS() {
    $theme = getActiveTheme();
    
    if (!$theme) {
        return '';
    }
    
    $css = ":root {\n";
    $css .= "    --primary: {$theme['primary_color']};\n";
    $css .= "    --primary-hover: {$theme['primary_hover']};\n";
    $css .= "    --primary-light: {$theme['primary_light']};\n";
    $css .= "    --dark-bg: {$theme['dark_bg']};\n";
    $css .= "    --card-bg: {$theme['card_bg']};\n";
    $css .= "    --overlay-bg: {$theme['overlay_bg']};\n";
    $css .= "    --light-text: {$theme['light_text']};\n";
    $css .= "    --muted-text: {$theme['muted_text']};\n";
    $css .= "    --border-color: {$theme['border_color']};\n";
    $css .= "}\n";
    
    return $css;
}
