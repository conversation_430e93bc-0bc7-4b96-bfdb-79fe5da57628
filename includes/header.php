<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><?php echo $GLOBALS['site_config']['title'] ?? 'ABLA - Asociación Bitcoin Latinoaméricana'; ?></title>
    <meta name="description" content="<?php echo $GLOBALS['site_config']['description'] ?? 'Promoviendo la adopción de Bitcoin en Latinoamérica'; ?>" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://<?php echo $GLOBALS['site_config']['domain'] ?? 'abla.lat'; ?><?php echo $_SERVER['REQUEST_URI']; ?>" />

    <!-- Meta tags para redes sociales -->
    <meta property="og:title" content="<?php echo $GLOBALS['site_config']['title'] ?? 'ABLA - Asociación Bitcoin Latinoaméricana'; ?>" />
    <meta property="og:description" content="<?php echo $GLOBALS['site_config']['description'] ?? 'Promoviendo la adopción de Bitcoin en Latinoamérica'; ?>" />
    <meta property="og:url" content="https://<?php echo $GLOBALS['site_config']['domain'] ?? 'abla.lat'; ?>" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://4tsix0yujj.ufs.sh/f/2vMRHqOYUHc0K5prXUEjR7oilQcqGuVyEA8Sm1pf4v95nLIB" />
    <meta property="og:image:alt" content="Asociación Bitcoin Latinoaméricana" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="https://4tsix0yujj.ufs.sh/f/2vMRHqOYUHc0K5prXUEjR7oilQcqGuVyEA8Sm1pf4v95nLIB" />

    <!-- Google Fonts - Titillium Web with fallback -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Titillium+Web:wght@300;400;600;700;900&display=swap" rel="stylesheet">

    <!-- Fallback fonts in case Google Fonts fails -->
    <style>
        @font-face {
            font-family: 'Titillium Web Fallback';
            src: local('Arial'), local('Helvetica'), local('sans-serif');
            font-weight: 300 900;
            font-display: swap;
        }

        body {
            font-family: 'Titillium Web', 'Titillium Web Fallback', Arial, Helvetica, sans-serif;
        }

        /* Basic fallback styles in case TailwindCSS doesn't load */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: #f97316;
            color: white;
        }

        .btn-primary:hover {
            background-color: #ea580c;
        }

        .btn-secondary {
            background-color: transparent;
            color: #f97316;
            border: 2px solid #f97316;
        }

        .btn-secondary:hover {
            background-color: #f97316;
            color: white;
        }

        .hidden {
            display: none !important;
        }

        @media (min-width: 768px) {
            .md\\:hidden {
                display: none !important;
            }

            .md\\:flex {
                display: flex !important;
            }
        }

        /* Fallback for Phosphor Icons */
        .ph {
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
        }

        /* Basic icon fallbacks using Unicode symbols */
        .ph-compass::before { content: "🧭"; }
        .ph-users::before { content: "👥"; }
        .ph-users-three::before { content: "👥"; }
        .ph-paper-plane-right::before { content: "✈️"; }
        .ph-envelope::before { content: "✉️"; }
        .ph-map-pin::before { content: "📍"; }
        .ph-globe::before { content: "🌐"; }
        .ph-instagram-logo::before { content: "📷"; }
        .ph-twitter-logo::before { content: "🐦"; }
        .ph-telegram-logo::before { content: "📱"; }
        .ph-github-logo::before { content: "💻"; }
        .ph-bitcoin-circle::before { content: "₿"; }
        .ph-currency-btc::before { content: "₿"; }
        .ph-lightning::before { content: "⚡"; }
        .ph-wallet::before { content: "💳"; }
        .ph-arrows-clockwise::before { content: "🔄"; }
        .ph-spinner::before { content: "⏳"; }
        .ph-bell-simple::before { content: "🔔"; }
        .ph-calendar::before { content: "📅"; }
        .ph-calendar-plus::before { content: "📅"; }
        .ph-chalkboard-teacher::before { content: "👨‍🏫"; }
        .ph-graduation-cap::before { content: "🎓"; }
        .ph-handshake::before { content: "🤝"; }
        .ph-lightbulb::before { content: "💡"; }
        .ph-database::before { content: "🗄️"; }
        .ph-database-export::before { content: "📤"; }
        .ph-database-plus::before { content: "🗄️"; }
        .ph-check-circle::before { content: "✅"; }
        .ph-warning-circle::before { content: "⚠️"; }
        .ph-eye::before { content: "👁️"; }
        .ph-plus-circle::before { content: "➕"; }
        .ph-article::before { content: "📄"; }
        .ph-user::before { content: "👤"; }
        .ph-hash::before { content: "#"; }
        .ph-chart-line-up::before { content: "📈"; }
        .ph-cube::before { content: "🧊"; }
    </style>

    <!-- TailwindCSS with error handling -->
    <script>
        // Check if TailwindCSS loads successfully
        window.tailwindLoaded = false;
    </script>
    <script src="https://cdn.tailwindcss.com" onload="window.tailwindLoaded = true;" onerror="console.warn('TailwindCSS failed to load from CDN')"></script>
    <script>
        // Configure Tailwind if it loaded successfully
        setTimeout(function() {
            if (window.tailwindLoaded && typeof tailwind !== 'undefined') {
                tailwind.config = {
                    theme: {
                        extend: {
                            fontFamily: {
                                'sans': ['"Titillium Web"', '"Titillium Web Fallback"', 'Arial', 'Helvetica', 'sans-serif'],
                            },
                        }
                    }
                }
            }
        }, 100);
    </script>

    <!-- Phosphor Icons with fallback -->
    <script>
        window.phosphorLoaded = false;
    </script>
    <script src="https://unpkg.com/@phosphor-icons/web" onload="window.phosphorLoaded = true;" onerror="console.warn('Phosphor Icons failed to load from CDN')"></script>

    <!-- Alpine.js with fallback -->
    <script>
        window.alpineLoaded = false;
    </script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" onload="window.alpineLoaded = true;" onerror="console.warn('Alpine.js failed to load from CDN')"></script>

    <!-- Fallback for Alpine.js functionality -->
    <script>
        // Simple fallback for mobile menu if Alpine.js fails
        setTimeout(function() {
            if (!window.alpineLoaded) {
                document.addEventListener('DOMContentLoaded', function() {
                    const mobileMenuButton = document.querySelector('[data-mobile-menu-button]');
                    const mobileMenu = document.querySelector('[data-mobile-menu]');

                    if (mobileMenuButton && mobileMenu) {
                        let isOpen = false;
                        mobileMenuButton.addEventListener('click', function() {
                            isOpen = !isOpen;
                            mobileMenu.style.display = isOpen ? 'block' : 'none';
                        });
                    }
                });
            }
        }, 2000);

        // Check for failed resources and log warnings
        setTimeout(function() {
            const failedResources = [];

            if (!window.tailwindLoaded) {
                failedResources.push('TailwindCSS');
            }

            if (!window.phosphorLoaded) {
                failedResources.push('Phosphor Icons');
            }

            if (!window.alpineLoaded) {
                failedResources.push('Alpine.js');
            }

            if (failedResources.length > 0) {
                console.warn('Some external resources failed to load:', failedResources.join(', '));
                console.info('The website is using fallback functionality to ensure proper operation.');
            }
        }, 3000);
    </script>

    <!-- Dynamic Theme CSS -->
    <link rel="stylesheet" href="/theme.css.php">

    <!-- Custom styles -->
    <link rel="stylesheet" href="/assets/css/styles.css">
</head>
<body class="font-sans" style="background-color: var(--dark-bg); color: var(--light-text);">
    <header style="background-color: var(--card-bg); color: var(--light-text);" x-data="{ mobileMenuOpen: false }">
        <div class="container mx-auto px-4 py-4 flex items-center justify-between">
            <a href="/" class="text-xl font-bold hover:text-gray-300 transition-colors">
                ASOCIACIÓN BITCOIN LATINOAMÉRICANA
            </a>
            <div class="hidden md:flex items-center space-x-8">
                <?php if ($GLOBALS['site_config']['is_one_page'] ?? false): ?>
                <!-- One-page navigation with smooth scroll -->
                <a href="#descubre" class="hover:text-gray-300 smooth-scroll">DESCUBRE</a>
                <a href="#eventos" class="hover:text-gray-300 smooth-scroll">EVENTOS</a>
                <a href="#nosotros" class="hover:text-gray-300 smooth-scroll">NOSOTROS</a>
                <a href="#sumate" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded smooth-scroll">SÚMATE</a>
                <?php else: ?>
                <!-- Multi-page navigation -->
                <a href="/descubre" class="hover:text-gray-300">DESCUBRE</a>
                <a href="/eventos" class="hover:text-gray-300">EVENTOS</a>
                <a href="/nosotros" class="hover:text-gray-300">NOSOTROS</a>
                <a href="/sumate" class="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded">SÚMATE</a>
                <?php endif; ?>
            </div>
            <button
                class="md:hidden text-white text-2xl focus:outline-none"
                @click="mobileMenuOpen = !mobileMenuOpen"
                data-mobile-menu-button
            >
                &#9776;
            </button>
        </div>
        <div class="md:hidden" x-show="mobileMenuOpen" data-mobile-menu style="background-color: var(--card-bg); display: none;">
            <ul class="px-4 pt-2 pb-4 space-y-2">
                <?php if ($GLOBALS['site_config']['is_one_page'] ?? false): ?>
                <!-- One-page navigation with smooth scroll -->
                <li><a href="#descubre" class="block hover:text-gray-300 smooth-scroll" @click="mobileMenuOpen = false">DESCUBRE</a></li>
                <li><a href="#eventos" class="block hover:text-gray-300 smooth-scroll" @click="mobileMenuOpen = false">EVENTOS</a></li>
                <li><a href="#nosotros" class="block hover:text-gray-300 smooth-scroll" @click="mobileMenuOpen = false">NOSOTROS</a></li>
                <li><a href="#sumate" class="block bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded smooth-scroll" @click="mobileMenuOpen = false">SÚMATE</a></li>
                <?php else: ?>
                <!-- Multi-page navigation -->
                <li><a href="/descubre" class="block hover:text-gray-300">DESCUBRE</a></li>
                <li><a href="/eventos" class="block hover:text-gray-300">EVENTOS</a></li>
                <li><a href="/nosotros" class="block hover:text-gray-300">NOSOTROS</a></li>
                <li><a href="/sumate" class="block bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded">SÚMATE</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </header>
    <main>