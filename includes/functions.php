<?php
/**
 * Connect to the database
 * 
 * @return mysqli|array Database connection or error array
 */
function connectDB() {
    global $db_config;
    
    if (!isset($db_config) || empty($db_config)) {
        // Load config if not already loaded
        include_once 'config.php';
    }
    
    try {
        $db = new mysqli(
            $db_config['host'], 
            $db_config['username'], 
            $db_config['password'], 
            $db_config['database']
        );
        
        if ($db->connect_error) {
            return [
                'error' => $db->connect_error,
                'code' => $db->connect_errno
            ];
        }
        
        $db->set_charset('utf8mb4');
        return $db;
    } catch (Exception $e) {
        return [
            'error' => $e->getMessage(),
            'code' => $e->getCode()
        ];
    }
}

/**
 * Check if a table exists in the database
 * 
 * @param mysqli $db Database connection
 * @param string $table Table name
 * @return bool True if table exists, false otherwise
 */
function tableExists($db, $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    return ($result && $result->num_rows > 0);
}

/**
 * Sanitize input data
 * 
 * @param string $data Data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Generate a slug from a string
 * 
 * @param string $string String to convert to slug
 * @return string Slug
 */
function generateSlug($string) {
    $string = strtolower($string);
    $string = preg_replace('/[^a-z0-9\s-]/', '', $string);
    $string = preg_replace('/[\s-]+/', '-', $string);
    $string = trim($string, '-');
    return $string;
}

/**
 * Format date to a readable format
 * 
 * @param string $date Date in Y-m-d format
 * @param string $format Output format (default: 'd/m/Y')
 * @return string Formatted date
 */
function formatDate($date, $format = 'd/m/Y') {
    $timestamp = strtotime($date);
    return date($format, $timestamp);
}

/**
 * Get active theme colors
 * 
 * @return array Theme colors or default colors if no theme is active
 */
function getActiveTheme() {
    $db = connectDB();
    if (is_array($db)) {
        // Connection error, return default theme
        return getDefaultTheme();
    }
    
    try {
        $result = $db->query("SELECT * FROM themes WHERE is_active = 1 LIMIT 1");
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
    } catch (Exception $e) {
        // Table might not exist yet
    }
    
    return getDefaultTheme();
}

/**
 * Get default theme colors
 * 
 * @return array Default theme colors
 */
function getDefaultTheme() {
    return [
        'name' => 'Bitcoin Orange',
        'is_active' => 1,
        'primary_color' => '#F7931A',
        'primary_hover' => '#FF9500',
        'primary_light' => '#FEF4E8',
        'dark_bg' => '#090C14',
        'card_bg' => '#13161F',
        'overlay_bg' => '#00000099',
        'light_text' => '#FFFFFF',
        'muted_text' => 'rgba(255, 255, 255, 0.7)',
        'border_color' => 'rgba(255, 255, 255, 0.1)'
    ];
}
