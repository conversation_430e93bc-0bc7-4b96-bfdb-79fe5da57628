<?php
/**
 * Simple Database Check Script
 * 
 * Quick diagnostic to see what's happening with the database
 */

require_once 'includes/db.php';

echo "<h1>Database Diagnostic</h1>";

// Test database connection
echo "<h2>1. Database Connection</h2>";
$db = connectDB();

if (is_array($db)) {
    echo "<p style='color: red;'>❌ Connection failed: " . $db['error'] . "</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Show current tables
echo "<h2>2. Current Tables</h2>";
$result = $db->query("SHOW TABLES");
if ($result && $result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_array()) {
        $table_name = $row[0];
        
        // Get row count
        $count_result = $db->query("SELECT COUNT(*) as count FROM `$table_name`");
        $row_count = $count_result ? $count_result->fetch_assoc()['count'] : 'Error';
        
        echo "<li><strong>$table_name</strong> - $row_count rows</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ No tables found</p>";
}

// Test database initialization
echo "<h2>3. Test Database Initialization</h2>";
echo "<p>Running initializeDatabase function...</p>";

$result = initializeDatabase($db);

if ($result['success']) {
    echo "<p style='color: green;'>✅ Database initialization completed successfully</p>";
    
    if (!empty($result['created_tables'])) {
        echo "<p>Created tables: " . implode(', ', $result['created_tables']) . "</p>";
    } else {
        echo "<p>No new tables created (they may already exist)</p>";
    }
    
    if (!empty($result['sample_data_added'])) {
        echo "<p>Sample data added:</p>";
        echo "<ul>";
        foreach ($result['sample_data_added'] as $item) {
            echo "<li>$item</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: red;'>❌ Database initialization failed</p>";
    
    if (!empty($result['errors'])) {
        echo "<p>Errors:</p>";
        echo "<ul>";
        foreach ($result['errors'] as $error) {
            echo "<li style='color: red;'>$error</li>";
        }
        echo "</ul>";
    }
}

// Show tables after initialization
echo "<h2>4. Tables After Initialization</h2>";
$result = $db->query("SHOW TABLES");
if ($result && $result->num_rows > 0) {
    echo "<ul>";
    while ($row = $result->fetch_array()) {
        $table_name = $row[0];
        
        // Get row count
        $count_result = $db->query("SELECT COUNT(*) as count FROM `$table_name`");
        $row_count = $count_result ? $count_result->fetch_assoc()['count'] : 'Error';
        
        echo "<li><strong>$table_name</strong> - $row_count rows</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Still no tables found</p>";
}

echo "<h2>5. Actions</h2>";
echo "<a href='/debug_db.php'>Full Debug Script</a> | ";
echo "<a href='/?route=admin'>Admin Dashboard</a> | ";
echo "<a href='/update_db.php'>Database Utility</a>";
?>
