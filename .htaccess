# Enable URL rewriting
RewriteEngine On

# Set the base directory
RewriteBase /

# Set domain name
RewriteCond %{HTTP_HOST} !^abla\.lat$ [NC]
RewriteCond %{HTTP_HOST} !^www\.abla\.lat$ [NC]
RewriteCond %{HTTP_HOST} !^localhost$ [NC]
RewriteCond %{HTTP_HOST} !^127\.0\.0\.1$ [NC]
RewriteRule ^(.*)$ https://abla.lat/$1 [R=301,L]

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteCond %{HTTP_HOST} !^localhost$ [NC]
RewriteCond %{HTTP_HOST} !^127\.0\.0\.1$ [NC]
RewriteRule ^(.*)$ https://abla.lat/$1 [R=301,L]

# Remove trailing slash
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [R=301,L]

# Handle one-page navigation
RewriteCond %{REQUEST_URI} ^/(descubre|eventos|nosotros|sumate|contacto)/?$ [NC]
RewriteRule ^(.*)$ /#$1 [R=301,L,NE]

# If the request is not for a file or directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Rewrite URLs for admin pages
RewriteRule ^admin/?$ index.php?route=admin [L,QSA]

# Rewrite URLs for specific content
RewriteRule ^eventos/([a-zA-Z0-9-]+)/?$ index.php?route=eventos&id=$1 [L,QSA]
RewriteRule ^iniciativas/([a-zA-Z0-9-]+)/?$ index.php?route=iniciativas&id=$1 [L,QSA]

# Handle all other routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?route=$1 [QSA,L]

# Handle 404 errors
ErrorDocument 404 /index.php?route=404

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Enable GZIP compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/pdf "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType application/x-javascript "access plus 1 month"
  ExpiresByType application/x-shockwave-flash "access plus 1 month"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresDefault "access plus 2 days"
</IfModule>

# Security headers
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
  Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com https://cdn.jsdelivr.net data:; img-src 'self' data: https:; connect-src 'self' https:; object-src 'none'; base-uri 'self';"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "^\.(?!well-known)|\.(?:git|htaccess|env|config\.php)$">
  Order allow,deny
  Deny from all
</FilesMatch>
