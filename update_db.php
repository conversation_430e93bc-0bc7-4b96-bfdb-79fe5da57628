<?php
/**
 * Database Update Script
 * 
 * Simple interface to run database updates safely.
 * This script provides a secure way to apply database migrations
 * to production environments.
 */

// Security check
session_start();

// Simple authentication
$auth_required = true;
$auth_password = 'abla_db_2024'; // Change this password

if ($auth_required) {
    if (!isset($_SESSION['db_util_authenticated'])) {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['password'])) {
            if ($_POST['password'] === $auth_password) {
                $_SESSION['db_util_authenticated'] = true;
            } else {
                $error = 'Contraseña incorrecta';
            }
        }
        
        if (!isset($_SESSION['db_util_authenticated'])) {
            ?>
            <!DOCTYPE html>
            <html lang="es">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Autenticación - Database Utility</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        background: #1a1a1a; 
                        color: #fff; 
                        display: flex; 
                        justify-content: center; 
                        align-items: center; 
                        min-height: 100vh; 
                        margin: 0; 
                    }
                    .auth-form { 
                        background: #2a2a2a; 
                        padding: 40px; 
                        border-radius: 10px; 
                        box-shadow: 0 0 20px rgba(0,0,0,0.5); 
                        text-align: center; 
                        max-width: 400px; 
                        width: 100%;
                    }
                    h1 { 
                        color: #f7931a; 
                        margin-bottom: 30px; 
                    }
                    input[type="password"] { 
                        width: 100%; 
                        padding: 15px; 
                        margin: 20px 0; 
                        border: 1px solid #444; 
                        border-radius: 5px; 
                        background: #333; 
                        color: #fff; 
                        font-size: 16px; 
                        box-sizing: border-box;
                    }
                    button { 
                        background: #f7931a; 
                        color: #000; 
                        padding: 15px 30px; 
                        border: none; 
                        border-radius: 5px; 
                        cursor: pointer; 
                        font-weight: bold; 
                        font-size: 16px; 
                        width: 100%;
                    }
                    button:hover { 
                        background: #ff9500; 
                    }
                    .error { 
                        color: #f88a8a; 
                        margin: 10px 0; 
                    }
                    .info { 
                        color: #8a8af8; 
                        margin: 20px 0; 
                        font-size: 14px; 
                    }
                </style>
            </head>
            <body>
                <div class="auth-form">
                    <h1>🔐 Database Utility</h1>
                    <p>Ingresa la contraseña para acceder a la utilidad de base de datos:</p>
                    
                    <?php if (isset($error)): ?>
                    <div class="error"><?php echo $error; ?></div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <input type="password" name="password" placeholder="Contraseña" required>
                        <button type="submit">Acceder</button>
                    </form>
                    
                    <div class="info">
                        Esta utilidad permite aplicar actualizaciones de base de datos de forma segura.
                    </div>
                </div>
            </body>
            </html>
            <?php
            exit;
        }
    }
}

// If authenticated, show the utility
define('DB_UTIL_ALLOWED', true);
require_once 'dbutil.php';
?>
