<?php
/**
 * Avatar Test Page
 * 
 * This page tests the avatar fetching functionality
 */

require_once 'includes/db.php';
require_once 'includes/avatar_fetcher.php';

echo "<h1>Avatar Test Page</h1>";

// Test avatar generation
echo "<h2>1. Test Default Avatar Generation</h2>";

$test_users = ['bitcoinbeach', 'myfirstbitcoin.io', 'bitcoinargentina'];

foreach ($test_users as $user) {
    $avatar = getDefaultAvatar($user, 'instagram');
    echo "<div style='margin: 10px; display: inline-block; text-align: center;'>";
    echo "<img src='$avatar' width='80' height='80' style='border-radius: 50%; border: 2px solid #F7931A;'>";
    echo "<br><small>$user</small>";
    echo "</div>";
}

echo "<h2>2. Test Instagram Avatar Fetching</h2>";

foreach ($test_users as $user) {
    echo "<h3>Testing: @$user</h3>";
    
    $result = getInstagramAvatar($user);
    
    echo "<div style='background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "<strong>Result:</strong> " . ($result['success'] ? 'Success' : 'Failed') . "<br>";
    echo "<strong>Method:</strong> " . $result['method'] . "<br>";
    echo "<strong>Source:</strong> " . $result['source'] . "<br>";
    echo "<strong>Avatar URL:</strong> " . substr($result['avatar_url'], 0, 100) . "...<br>";
    echo "<img src='{$result['avatar_url']}' width='60' height='60' style='border-radius: 50%; border: 2px solid #ccc; margin-top: 10px;'>";
    echo "</div>";
}

echo "<h2>3. Test Database Integration</h2>";

$db = connectDB();
if (is_array($db)) {
    echo "<p style='color: red;'>Database connection failed: " . $db['error'] . "</p>";
} else {
    echo "<p style='color: green;'>Database connected successfully</p>";
    
    // Get initiatives from database
    $result = $db->query("SELECT * FROM initiatives LIMIT 3");
    
    if ($result && $result->num_rows > 0) {
        echo "<h3>Current Initiatives with Avatars:</h3>";
        
        while ($initiative = $result->fetch_assoc()) {
            $avatar_url = getAvatarForDisplay($initiative);
            
            echo "<div style='background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #F7931A;'>";
            echo "<div style='display: flex; align-items: center; gap: 15px;'>";
            echo "<img src='$avatar_url' width='50' height='50' style='border-radius: 50%; border: 2px solid #F7931A;'>";
            echo "<div>";
            echo "<strong>{$initiative['name']}</strong><br>";
            
            if (!empty($initiative['instagram_user'])) {
                echo "<span style='color: #E4405F;'>📷 {$initiative['instagram_user']}</span><br>";
            }
            
            if (!empty($initiative['twitter_user'])) {
                echo "<span style='color: #1DA1F2;'>🐦 {$initiative['twitter_user']}</span><br>";
            }
            
            echo "<small>Avatar Source: " . ($initiative['avatar_source'] ?? 'default') . "</small>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
    } else {
        echo "<p>No initiatives found in database.</p>";
    }
}

echo "<h2>4. Actions</h2>";
echo "<a href='/migrate_initiatives_avatar.php' style='background: #F7931A; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 Run Migration</a>";
echo "<a href='/?route=admin' style='background: #333; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Admin Dashboard</a>";
echo "<a href='/debug_db.php' style='background: #666; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Debug Database</a>";

echo "<h2>5. Manual Avatar Test</h2>";
echo "<form method='POST' style='background: #f0f0f0; padding: 20px; border-radius: 8px;'>";
echo "<label>Instagram Username: <input type='text' name='test_instagram' placeholder='bitcoinbeach' style='padding: 5px; margin: 5px;'></label><br>";
echo "<label>Twitter Username: <input type='text' name='test_twitter' placeholder='bitcoin' style='padding: 5px; margin: 5px;'></label><br>";
echo "<button type='submit' name='test_fetch' style='background: #F7931A; color: white; padding: 10px 15px; border: none; border-radius: 5px; margin: 10px 0;'>Test Avatar Fetch</button>";
echo "</form>";

if (isset($_POST['test_fetch'])) {
    $test_instagram = $_POST['test_instagram'] ?? '';
    $test_twitter = $_POST['test_twitter'] ?? '';
    
    echo "<h3>Manual Test Results:</h3>";
    
    if ($test_instagram) {
        echo "<h4>Instagram: @$test_instagram</h4>";
        $ig_result = getInstagramAvatar($test_instagram);
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
        echo "<strong>Success:</strong> " . ($ig_result['success'] ? 'Yes' : 'No') . "<br>";
        echo "<strong>Method:</strong> " . $ig_result['method'] . "<br>";
        echo "<img src='{$ig_result['avatar_url']}' width='80' height='80' style='border-radius: 50%; margin: 10px 0;'>";
        echo "</div>";
    }
    
    if ($test_twitter) {
        echo "<h4>Twitter: @$test_twitter</h4>";
        $tw_result = getTwitterAvatar($test_twitter);
        echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
        echo "<strong>Success:</strong> " . ($tw_result['success'] ? 'Yes' : 'No') . "<br>";
        echo "<strong>Method:</strong> " . $tw_result['method'] . "<br>";
        echo "<img src='{$tw_result['avatar_url']}' width='80' height='80' style='border-radius: 50%; margin: 10px 0;'>";
        echo "</div>";
    }
}
?>
