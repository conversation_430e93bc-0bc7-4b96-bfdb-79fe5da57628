
<html lang="es">
      <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>ABLA</title>
          
          <!-- Meta tags para redes sociales -->
          <meta property="og:image" content="https://4tsix0yujj.ufs.sh/f/2vMRHqOYUHc0K5prXUEjR7oilQcqGuVyEA8Sm1pf4v95nLIB" />
          <meta property="og:image:alt" content="Imagen del proyecto" />
          <meta name="twitter:card" content="summary_large_image" />
          <meta name="twitter:image" content="https://4tsix0yujj.ufs.sh/f/2vMRHqOYUHc0K5prXUEjR7oilQcqGuVyEA8Sm1pf4v95nLIB" />

          <!-- TailwindCSS CDN -->
          <script src="https://cdn.tailwindcss.com"></script>

          <!-- React & Babel -->
          <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
          <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
        </head>
        <body>
          <div id="root"></div>

          <script type="text/babel">
const { useState, useEffect, useRef, useMemo, createContext, useContext, StrictMode } = React;
            const { createRoot } = ReactDOM;

            // /components/AboutSection.js
const AboutSection = () => {
  return (
    <section className="bg-gray-100 py-16 px-4">
      <div className="container mx-auto">
        <h2 className="text-4xl font-bold text-center mb-12">Sobre ABLA</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Tarjeta 1 */}
          <div className="bg-white p-8 rounded-2xl shadow-md"> {/* Ajustando padding y sombra */}
            <h3 className="text-2xl font-semibold mb-4">Nuestra Misión</h3>
            <p className="text-gray-700">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
          {/* Tarjeta 2 */}
          <div className="bg-white p-8 rounded-2xl shadow-md"> {/* Ajustando padding y sombra */}
            <h3 className="text-2xl font-semibold mb-4">Nuestra Visión</h3>
            <p className="text-gray-700">
              Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
            </p>
          </div>
          {/* Tarjeta 3 */}
          <div className="bg-white p-8 rounded-2xl shadow-md"> {/* Ajustando padding y sombra */}
            <h3 className="text-2xl font-semibold mb-4">Nuestros Valores</h3>
            <p className="text-gray-700">
              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};



// DONE

// /components/EventCard.js
const EventCard = ({ event }) => {
  return (
    <div className="bg-white p-6 rounded-2xl shadow-md flex flex-col"> {/* Sombra y bordes según Figma */}
      <h3 className="text-xl font-semibold mb-2">{event.title}</h3>
      <p className="text-gray-600 text-sm mb-4">{event.date}</p>
      <p className="text-gray-700 flex-grow">{event.description}</p>
      <button className="mt-4 px-4 py-2 bg-orange-500 text-white text-sm font-semibold rounded-lg hover:bg-orange-600 transition-colors self-start">
        Ver Detalles
      </button>
    </div>
  );
};

// /components/EventsSection.js
// Mock data para eventos (debería ir en mock/events.js)
const mockEvents = [
  { id: 1, title: 'Meetup Bitcoin CDMX', date: '15 de Octubre, 2024', description: 'Charla sobre la adopción de Bitcoin en México.' },
  { id: 2, title: 'Workshop Lightning Network', date: '20 de Noviembre, 2024', description: 'Aprende a usar y desarrollar con Lightning Network.' },
  { id: 3, title: 'Conferencia ABLA 2025', date: 'Febrero 2025', description: 'El evento más grande de Bitcoin en Latinoamérica.' },
];

const EventsSection = () => {
  return (
    <section className="bg-black text-white py-16 px-4">
      <div className="container mx-auto">
        <h2 className="text-4xl font-bold text-center mb-12">Próximos Eventos</h2> {/* Espaciado inferior ajustado */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mockEvents.map(event => (
            <EventCard key={event.id} event={event} />
          ))}
        </div>
      </div>
    </section>
  );
};



// DONE

// /components/InfoSection.js
const InfoSection = () => {
  return (
    <section className="bg-black text-white py-16 px-4">
      <div className="container mx-auto">
        <h2 className="text-4xl font-bold text-center mb-16">Información ABLA</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Tarjeta 1 */}
          <div className="bg-white text-black p-8 rounded-2xl shadow-md flex flex-col">
            <h3 className="text-2xl font-semibold mb-4">Educación Bitcoin</h3>
            <p className="text-gray-700 flex-grow">
              Promovemos la educación sobre Bitcoin y su tecnología en toda Latinoamérica.
            </p>
            <button className="mt-6 px-6 py-2 bg-orange-500 text-white font-semibold rounded-lg hover:bg-orange-600 transition-colors self-start">
              Más información
            </button>
          </div>
          
          {/* Tarjeta 2 */}
          <div className="bg-white text-black p-8 rounded-2xl shadow-md flex flex-col">
            <h3 className="text-2xl font-semibold mb-4">Comunidad</h3>
            <p className="text-gray-700 flex-grow">
              Conectamos a entusiastas, desarrolladores y empresarios de Bitcoin en toda la región.
            </p>
            <button className="mt-6 px-6 py-2 bg-orange-500 text-white font-semibold rounded-lg hover:bg-orange-600 transition-colors self-start">
              Únete
            </button>
          </div>
          
          {/* Tarjeta 3 */}
          <div className="bg-white text-black p-8 rounded-2xl shadow-md flex flex-col">
            <h3 className="text-2xl font-semibold mb-4">Recursos</h3>
            <p className="text-gray-700 flex-grow">
              Ofrecemos materiales educativos y herramientas en español para facilitar la adopción.
            </p>
            <button className="mt-6 px-6 py-2 bg-orange-500 text-white font-semibold rounded-lg hover:bg-orange-600 transition-colors self-start">
              Explorar
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

// /components/HeroSection.js
const HeroSection = () => {
  return (
    <section className="bg-black text-white py-20">
      <div className="container mx-auto px-4">
        {/* Ajustando el interlineado */}
        <h1 className="text-8xl sm:text-9xl lg:text-10xl font-extrabold uppercase leading-none text-left"> {/* Cambiando leading-tight a leading-none */}
          <span className="block">SE</span>
          <span className="block tracking-tighter">
            <span className="text-white inline-block">H</span>
            <span className="text-orange-500 inline-block">ABLA</span>
          </span>
          <span className="block">ESPAÑOL</span>
        </h1>
        <p className="italic mt-6 text-lg text-center max-w-4xl mx-auto">
          Somos el grito manso de la descentralización en América Latina, donde las fronteras son solamente para aquellos que le temen al infinito, la revolución que no será televisada
        </p>
      </div>
    </section>
  );
};



// DONE

// /components/LayoutFooter.js
const LayoutFooter = () => {
  return (
    <footer className="bg-black text-gray-400 py-8 px-4">
      <div className="container mx-auto text-center text-sm italic">
        &copy; 2025 Asociación Bitcoin Latinoamérica. Todos los derechos reservados.
      </div>
    </footer>
  );
};

// /components/IniciativasPage.js
const IniciativasPage = () => {
  return (
    <section className="bg-white text-black py-16 px-4 min-h-screen">
      <div className="container mx-auto">
        <h2 className="text-4xl font-bold text-center mb-8">Nuestras Iniciativas</h2>
        <p className="text-lg text-gray-700 text-center">
          Conoce los proyectos y actividades que impulsamos en la comunidad.
        </p>
        {/* Aquí puedes agregar contenido específico para la sección Iniciativas */}
      </div>
    </section>
  );
};

// /components/SumatePage.js
const SumatePage = () => {
  return (
    <section className="bg-white text-black py-16 px-4 min-h-screen">
      <div className="container mx-auto">
        <h2 className="text-4xl font-bold text-center mb-8">Súmate a ABLA</h2>
        <p className="text-lg text-gray-700 text-center">
          Únete a nuestra comunidad y participa activamente en la adopción de Bitcoin.
        </p>
        {/* Aquí puedes agregar contenido específico para la sección Súmate */}
      </div>
    </section>
  );
};

// /components/FounderPopup.js
const FounderPopup = ({ onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white text-black p-8 rounded-lg shadow-xl max-w-sm w-full text-center relative">
        {/* Botón para cerrar el popup */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl leading-none"
        >
          &times;
        </button>

        <h3 className="text-2xl font-bold mb-4">CONVOCATORIA DE SOCIOS FUNDADORES</h3>
        <p className="text-gray-700 mb-6">
          ABLA está dando sus primeros pasos y hasta el 22-07 recibirá solicitudes de organizaciones e iniciativas que quieran formar parte.
        </p>
        <button className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors">
          CONOCE MAS
        </button>
      </div>
    </div>
  );
};

// /components/InitiativeCard.js
const InitiativeCard = ({ initiative }) => {
  return (
    <a
      href={initiative.instagramLink}
      target="_blank"
      rel="noopener noreferrer"
      className="bg-gray-800 text-white p-6 rounded-2xl shadow-md flex flex-col hover:bg-gray-700 transition-colors"
    >
      <h3 className="text-xl font-semibold mb-2">{initiative.name}</h3>
      <p className="text-orange-500 text-sm mb-4">{initiative.instagramUser}</p>
      <p className="text-gray-300 text-sm flex-grow">{initiative.description}</p>
      {/* Puedes agregar un ícono de Instagram aquí si lo deseas */}
    </a>
  );
};

// /components/ShareInitiativePopup.js
const ShareInitiativePopup = ({ onClose, onShare }) => {
  const [instagramHandle, setInstagramHandle] = useState('');
  const [showThankYou, setShowThankYou] = useState(false);

  const handleSend = () => {
    // Aquí podrías agregar lógica para enviar el handle a algún lugar (ej. una API, un email)
    console.log('Iniciativa compartida:', instagramHandle);
    setShowThankYou(true);
    // Opcional: cerrar el popup después de un tiempo
    // setTimeout(() => {
    //   onClose();
    // }, 2000);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white text-black p-8 rounded-lg shadow-xl max-w-sm w-full text-center relative">
        {/* Botón para cerrar el popup */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl leading-none"
        >
          &times;
        </button>

        {!showThankYou ? (
          <>
            <h3 className="text-2xl font-bold mb-4">¡Compartinos tu @!</h3>
            <input
              type="text"
              placeholder="@usuario_instagram"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg mb-6 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={instagramHandle}
              onChange={(e) => setInstagramHandle(e.target.value)}
            />
            <button
              onClick={handleSend}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors"
            >
              Enviar
            </button>
          </>
        ) : (
          <div className="flex flex-col items-center">
            {/* SVG de Corazón (ejemplo simple) */}
            <svg className="w-12 h-12 text-red-500 mb-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"></path>
            </svg>
            <p className="text-xl font-semibold text-green-600">¡Gracias por compartir amor!</p>
          </div>
        )}
      </div>
    </div>
  );
};

// /components/EventosPageNuevo.js
const EventosPageNuevo = () => { // Nuevo nombre del componente
  return (
    <section className="bg-black text-white min-h-screen py-16 px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Título principal */}
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
          EVENTOS
        </h1>

        {/* Cita en "nube" */}
        <div className="relative bg-gray-800 rounded-3xl p-6 mb-12">
          <div className="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
          <p className="text-xl italic">
            "La revolución no será televisada, será descentralizada." – Desconocido
          </p>
        </div>

        {/* Contenido (Lista de iniciativas, que ahora serán eventos) */}
        <div className="space-y-6 text-lg">
          <p>ABLA impulsa diversos eventos para promover la adopción y educación sobre Bitcoin en América Latina:</p>
          <ul className="list-disc list-inside space-y-2">
            <li><strong>Educación Bitcoin</strong>: Talleres y cursos gratuitos sobre fundamentos de Bitcoin, seguridad y uso de wallets.</li>
            <li><strong>Meetups Regionales</strong>: Eventos presenciales y virtuales para conectar a la comunidad Bitcoin en diferentes países.</li>
            <li><strong>Investigación</strong>: Publicación de informes sobre el estado de Bitcoin en la región y su impacto económico.</li>
            <li><strong>Desarrollo</strong>: Apoyo a proyectos open-source que mejoren la infraestructura Bitcoin en español.</li>
            <li><strong>Adopción Comercial</strong>: Programas para incentivar a comercios a aceptar Bitcoin como medio de pago.</li>
          </ul>
        </div>
      </div>
    </section>
  );
};

// /components/InitiativeCardVisual.js
const InitiativeCardVisual = ({ initiative }) => {
  return (
    <a
      href={initiative.instagramLink}
      target="_blank"
      rel="noopener noreferrer"
      className="bg-gray-800 text-white p-6 rounded-2xl shadow-md flex flex-col hover:bg-gray-700 transition-colors"
    >
      {/* Sección de Perfil */}
      <div className="flex items-center mb-4">
        {/* Placeholder para Foto de Perfil */}
        <div className="w-12 h-12 rounded-full bg-gray-600 flex-shrink-0">
          {/* Aquí iría la imagen de perfil */}
        </div>
        <div className="ml-4">
          <h3 className="text-xl font-semibold">{initiative.name}</h3>
          <p className="text-orange-500 text-sm">{initiative.instagramUser}</p>
        </div>
      </div>

      {/* Descripción - Ajustando para manejar texto largo */}
      <p className="text-gray-300 text-sm mb-4 flex-grow overflow-hidden text-ellipsis"> {/* Agregando overflow y text-ellipsis */}
        {initiative.description}
      </p>

      {/* Sección de Últimas Fotos (Placeholders) */}
      <div className="grid grid-cols-3 gap-2 mt-4">
        {/* Placeholder para Foto 1 */}
        <div className="w-full h-20 bg-gray-700 rounded-md">
          {/* Aquí iría la imagen del feed */}
        </div>
        {/* Placeholder para Foto 2 */}
        <div className="w-full h-20 bg-gray-700 rounded-md">
          {/* Aquí iría la imagen del feed */}
        </div>
        {/* Placeholder para Foto 3 */}
        <div className="w-full h-20 bg-gray-700 rounded-md">
          {/* Aquí iría la imagen del feed */}
        </div>
      </div>
    </a>
  );
};



// DONE

// /mock/initiatives.js
// Datos simulados para las iniciativas (debería ir en mock/initiatives.js)
const initiatives = [
  {
    id: 1,
    name: 'Mi Primer Bitcoin – El Salvador',
    instagramUser: '@myfirstbitcoin.io',
    instagramLink: 'https://www.instagram.com/myfirstbitcoin.io/',
    description: 'Pioneros en educación Bitcoin en El Salvador. Ofrecen el primer Diploma Bitcoin del mundo, con impacto nacional e internacional.',
  },
  {
    id: 2,
    name: 'Escuelita Bitcoin – Uruguay',
    instagramUser: '@escuelitabitcoin',
    instagramLink: 'https://www.instagram.com/escuelitabitcoin/',
    description: 'Talleres gratuitos de Bitcoin y finanzas para niñas, niños, familias y docentes en zonas rurales de Uruguay.',
  },
  {
    id: 3,
    name: 'Escuelita Bitcoin – Paraguay (CriptoPY)',
    instagramUser: '@cripto.py',
    instagramLink: 'https://www.instagram.com/cripto.py/',
    description: 'Educación Bitcoin desde Paraguay con enfoque comunitario y lúdico, articulando eventos y acciones en colegios e instituciones.',
  },
  {
    id: 4,
    name: 'LABITCONF',
    instagramUser: '@labitconf',
    instagramLink: 'https://www.instagram.com/labitconf/',
    description: 'Conferencia de Bitcoin y Blockchain más importante de América Latina. Desde 2013 une educadores, desarrolladores y activistas.',
  },
  {
    id: 5,
    name: 'ONG Bitcoin Argentina',
    instagramUser: '@ongbitcoinargentina',
    instagramLink: 'https://www.instagram.com/ongbitcoinargentina/',
    description: 'ONG dedicada a promover el uso responsable de Bitcoin a través de educación, investigación y eventos.',
  },
  {
    id: 6,
    name: 'Club del Bitcoin Perú',
    instagramUser: '@clubdelbitcoinperuoficial',
    instagramLink: 'https://www.instagram.com/clubdelbitcoinperuoficial/',
    description: 'Comunidad activa que promueve la adopción de Bitcoin mediante charlas, cursos y asesoramiento a nuevos usuarios.',
  },
  {
    id: 7,
    name: 'La Crypta – Argentina',
    instagramUser: '@lacryptaok',
    instagramLink: 'https://www.instagram.com/lacryptaok/',
    description: 'Espacio maximalista que educa sobre Bitcoin y soberanía financiera. Enfoque libertario y anti-inflación.',
  },
  {
    id: 8,
    name: 'Bitcoin Beach – El Salvador',
    instagramUser: '@bitcoinbeach',
    instagramLink: 'https://www.instagram.com/bitcoinbeach/',
    description: 'Proyecto en El Zonte que creó una economía circular Bitcoin. Inspiró la Ley Bitcoin en El Salvador.',
  },
  {
    id: 9,
    name: 'Área Bitcoin – Argentina',
    instagramUser: '@areabitcoin',
    instagramLink: 'https://www.instagram.com/areabitcoin/',
    description: 'Escuela de educación descentralizada. Promueve la autonomía financiera desde una perspectiva pro-Bitcoin y comunitaria.',
  },
  {
    id: 10,
    name: 'RedTraSex (uso de BTC para inclusión financiera)',
    instagramUser: '@redtrasex',
    instagramLink: 'https://www.instagram.com/redtrasex/',
    description: 'Red de trabajadoras sexuales de América Latina que usa Bitcoin para empoderamiento financiero y protección de derechos.',
  },
  {
    id: 11,
    name: 'Bitcoin Chile (Asociación Gremial)',
    instagramUser: '@bitcoinchile',
    instagramLink: 'https://www.instagram.com/bitcoinchile/',
    description: 'Asociación que fomenta el uso de Bitcoin en Chile, con actividades educativas y defensa de políticas públicas abiertas.',
  },
  {
    id: 12,
    name: 'Bitcoin Iberoamérica',
    instagramUser: '@bitcoiniberoamerica',
    instagramLink: 'https://www.instagram.com/bitcoiniberoamerica/',
    description: 'Fundación que apoya eventos, contenidos educativos y alianzas para difundir Bitcoin en países hispanohablantes.',
  },
  {
    id: 13,
    name: 'CryptoVendimia – Mendoza, Argentina',
    instagramUser: '@cryptovendimia',
    instagramLink: 'https://www.instagram.com/cryptovendimia/',
    description: 'Evento anual que combina cultura vitivinícola con educación Bitcoin. Fomenta el turismo y la economía circular con BTC.',
  },
  {
    id: 14,
    name: 'La Bitcoineta',
    instagramUser: '@bitcoineta',
    instagramLink: 'https://www.instagram.com/bitcoineta?igsh=MXg3dWRqM25xOHk4Ng==',
    description: 'Puente entre quienes desarrollan tecnologías que cambian el mundo y los ecosistemas productivos, sociales, culturales y económicos.',
  },
  {
    id: 15,
    name: 'Bitcoin Paraguay',
    instagramUser: '@bitcoinparaguay',
    instagramLink: 'https://www.instagram.com/bitcoinparaguay/',
    description: 'Comunidad Bitcoin de Paraguay.',
  },
];


// DONE

// /components/DescubrePage.js
const DescubrePage = () => {
  const [showSharePopup, setShowSharePopup] = useState(false);

  const handleOpenSharePopup = () => {
    setShowSharePopup(true);
  };

  const handleCloseSharePopup = () => {
    setShowSharePopup(false);
  };

  const handleShareInitiative = (handle) => {
    console.log('Iniciativa compartida:', handle);
  };

  return (
    <section className="bg-black text-white min-h-screen py-16 px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Título principal con "ABLA" en naranja */}
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
          DESCUBRE <span className="text-orange-500">ABLA</span>
        </h1>

        {/* Cita en "nube" */}
        <div className="relative bg-gray-800 rounded-3xl p-6 mb-12">
          <div className="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
          <p className="text-xl italic">
            "Bitcoin es la primera herramienta monetaria que no puede ser confiscada, censurada o devaluada por ningún gobierno o institución." – Michael Saylor
          </p>
        </div>

        {/* Contenido */}
        <div className="space-y-6 text-lg mb-12">
          <p>
            Bitcoin es mucho más que una moneda digital: es un protocolo de consenso descentralizado que permite transferir valor sin intermediarios. Su diseño ingenioso resuelve el problema del doble gasto sin necesidad de una autoridad central, mediante la prueba de trabajo y la cadena de bloques.
          </p>

          <p>
            En América Latina, donde la confianza en las instituciones financieras tradicionales se ha erosionado, Bitcoin ofrece una alternativa soberana. Países como El Salvador ya han adoptado Bitcoin como moneda de curso legal, mientras que en Argentina y Venezuela su uso como reserva de valor frente a la inflación es cada vez más común.
          </p>
        </div>

        {/* Sección de Iniciativas con tarjetas visuales */}
        <div className="mt-16">
          <h2 className="text-4xl font-bold text-center mb-12">Iniciativas en Latinoamérica</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {initiatives.map(initiative => (
              <InitiativeCardVisual key={initiative.id} initiative={initiative} />
            ))}
          </div>
        </div>

        {/* Botón para compartir iniciativa */}
        <div className="flex justify-center mt-12">
          <button
            onClick={handleOpenSharePopup}
            className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-lg transition-colors text-lg"
          >
            ¿FALTA TU INICIATIVA?
          </button>
        </div>

        {/* Popup de compartir iniciativa */}
        {showSharePopup && <ShareInitiativePopup onClose={handleCloseSharePopup} onShare={handleShareInitiative} />}
      </div>
    </section>
  );
};



// DONE

// /components/NosotrosPage.js
const NosotrosPage = ({ navigateTo }) => { // Recibiendo navigateTo como prop
  return (
    <section className="bg-black text-white min-h-screen py-16 px-4">
      <div className="container mx-auto max-w-4xl">
        {/* Título principal con "VOS" en naranja */}
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
          NOSOTROS SOMOS <span className="text-orange-500">VOS</span>
        </h1>

        {/* Cita en "nube" */}
        <div className="relative bg-gray-800 rounded-3xl p-6 mb-12">
          <div className="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
          <p className="text-xl italic">
            "Nadie libera a nadie, ni nadie se libera solo. Los hombres se liberan en comunión." – Paulo Freire
          </p>
        </div>

        {/* Contenido */}
        <div className="space-y-6 text-lg mb-12">
          <p>
            Nuestra América Latina ha sufrido siglos de opresión: antes con cadenas visibles, hoy con ataduras invisibles impuestas por sistemas financieros centralizados. Desde el altiplano andino hasta la pampa argentina, nuestros pueblos han enfrentado inflación descontrolada, exclusión bancaria y dependencia monetaria extranjera. Nos han tratado como simples receptáculos pasivos, tal como Freire denunció en la educación bancaria, pero ya no aceptaremos ser “alumnos” dóciles del orden económico opresor. Somos protagonistas conscientes de nuestra propia liberación financiera, dispuestos a preguntar, aprender y actuar sobre nuestro destino económico.
          </p>

          <p>
            Bitcoin es nuestra herramienta de emancipación. Nacido de la gente para la gente, Bitcoin no responde a ningún gobierno ni corporación; responde solo a la verdad matemática y al consenso libre. Representa el “dinero sano” del que hablaron los pensadores de la escuela austríaca: una moneda inmune a la imprenta arbitraria y a la tiranía inflacionaria. Como señaló Ludwig von Mises, “la moneda sana fue concebida como un instrumento para la protección de las libertades civiles contra las incursiones despóticas de los gobiernos”. Bitcoin encarna ese principio, poniéndole un freno voluntario al abuso monetario estatal y devolviendo el poder económico a cada individuo.
          </p>

          {/* Continuación y final del texto */}
          <p>
            Queremos unir todas las iniciativas que recorren nuestros países, en una riqueza que no conoce de fronteras, que sabe de luchas y de salir adelante juntos. ABLA no busca ser una iniciativa más, sino una plataforma para que se amplifique el hermoso trabajo que están realizando miles de personas a través de cientos de iniciativas en todo el continente. ABLA es un lugar de encuentro, de comunidad.
          </p>
        </div>

        {/* Sección Fundadores */}
        <div className="mt-16">
          <h2 className="text-4xl font-bold uppercase mb-8">FUNDADORES</h2>
          <p className="text-lg mb-8">
            En el contexto del evento Bitcoin más grande que unirá Latinoamérica este año, no queremos dejar pasar la oportunidad para encontrarnos (física y virtualmente) y construir un camino descentralizado de sueños compartidos. Si estás construyendo comunidades libres, empoderadas y autodidactas, ABLA es tu lugar. Hacé click en <button onClick={() => navigateTo('sumate')} className="text-orange-500 hover:underline font-semibold">SÚMATE</button> para conocer más.
          </p>
        </div>


        {/* Video embedido centrado */}
        <div className="flex justify-center mt-12">
          <iframe width="560" height="315" src="https://www.youtube.com/embed/qb4M1lfDJSA?si=6Q66ukxoHnHuFTuR&amp;start=15" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
        </div>
      </div>
    </section>
  );
};

// /components/LayoutHeader.js
const LayoutHeader = ({ navigateTo }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-black text-white">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Haciendo el texto del logo un botón */}
        <button
          onClick={() => navigateTo('home')}
          className="text-xl font-bold cursor-pointer hover:text-gray-300 transition-colors" // Agregando clases de botón y hover
        >
          ASOCIACIÓN BITCOIN LATINOAMÉRICA
        </button>
        <div className="hidden md:flex items-center space-x-8">
          <button onClick={() => navigateTo('descubre')} className="hover:text-gray-300">DESCUBRE</button>
          <button onClick={() => navigateTo('eventos')} className="hover:text-gray-300">EVENTOS</button>
          <button onClick={() => navigateTo('nosotros')} className="hover:text-gray-300">NOSOTROS</button>
          <button onClick={() => navigateTo('sumate')} className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded">SÚMATE</button>
        </div>
        <button
          className="md:hidden text-white text-2xl focus:outline-none"
          onClick={toggleMobileMenu}
        >
          &#9776;
        </button>
      </div>
      <div className={`${isMobileMenuOpen ? '' : 'hidden'} md:hidden bg-black`}>
        <ul className="px-4 pt-2 pb-4 space-y-2">
          <li><button onClick={() => navigateTo('descubre')} className="block hover:text-gray-300">DESCUBRE</button></li>
          <li><button onClick={() => navigateTo('eventos')} className="block hover:text-gray-300">EVENTOS</button></li>
          <li><button onClick={() => navigateTo('nosotros')} className="block hover:text-gray-300">NOSOTROS</button></li>
          <li><button onClick={() => navigateTo('sumate')} className="block bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded">SÚMATE</button></li>
        </ul>
      </div>
    </header>
  );
};



// DONE

// /App.js
const App = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [showPopup, setShowPopup] = useState(true);

  useEffect(() => {
    setShowPopup(true);
  }, []);

  const navigateTo = (page) => {
    setCurrentPage(page);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return (
          <>
            <HeroSection />
            {/* Aquí puedes agregar más secciones de la landing si las tienes */}
          </>
        );
      case 'descubre':
        return <DescubrePage navigateTo={navigateTo} />;
      case 'eventos':
        return <EventosPageNuevo navigateTo={navigateTo} />;
      case 'nosotros':
        return <NosotrosPage navigateTo={navigateTo} />;
      case 'sumate':
        return <SumatePage navigateTo={navigateTo} />;
      default:
        return (
          <>
            <HeroSection />
            {/* Página por defecto */}
          </>
        );
    }
  };

  return (
    // Asegurándonos de que el fondo del contenedor principal sea negro
    <div className="font-sans bg-black min-h-screen"> {/* Agregando bg-black y min-h-screen */}
      {showPopup && <FounderPopup onClose={handleClosePopup} />}
      <LayoutHeader navigateTo={navigateTo} />
      <main>
        {renderPage()}
      </main>
      <LayoutFooter />
    </div>
  );
};



// DONE

      const root = createRoot(document.getElementById("root"));
            root.render(
              <StrictMode>
                <App />
              </StrictMode>
            );
</script>
        </body>
      </html>