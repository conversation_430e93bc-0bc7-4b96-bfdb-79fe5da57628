-- Create database
CREATE DATABASE IF NOT EXISTS ablat;
USE ablat;

-- Create tables
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    subtitle VARCHAR(255),
    content TEXT,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS initiatives (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    instagram_user VARCHAR(100),
    instagram_link VARCHAR(255),
    description TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    location VARCHAR(255),
    image_url VARCHAR(255),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert admin user (password: admin123)
INSERT INTO users (username, password, email) VALUES
('admin', '$2y$10$8tGmGzgvGjHoOQ5Kgh0GQOj8sMqH9jL5PUlC9UbNWvf5wGtyPi.Aq', '<EMAIL>');

-- Insert sample content
INSERT INTO content (section, title, subtitle, content) VALUES
('hero', 'SE HABLA ESPAÑOL', '', 'Asociación Bitcoin Latinoamérica'),
('about', 'Sobre ABLA', '', 'ABLA es una asociación dedicada a promover la adopción de Bitcoin en Latinoamérica.');

-- Insert sample initiatives
INSERT INTO initiatives (name, instagram_user, instagram_link, description) VALUES
('Mi Primer Bitcoin – El Salvador', '@myfirstbitcoin.io', 'https://www.instagram.com/myfirstbitcoin.io/', 'Pioneros en educación Bitcoin en El Salvador. Ofrecen el primer Diploma Bitcoin del mundo, con impacto nacional e internacional.'),
('Escuelita Bitcoin – Uruguay', '@escuelitabitcoin', 'https://www.instagram.com/escuelitabitcoin/', 'Talleres gratuitos de Bitcoin y finanzas para niñas, niños, familias y docentes en zonas rurales de Uruguay.'),
('Bitcoin Paraguay', '@bitcoinparaguay', 'https://www.instagram.com/bitcoinparaguay/', 'Comunidad Bitcoin de Paraguay.');

-- Insert sample events
INSERT INTO events (title, date, description, location) VALUES
('Meetup Bitcoin CDMX', '2024-10-15', 'Charla sobre la adopción de Bitcoin en México.', 'Ciudad de México'),
('Workshop Lightning Network', '2024-11-20', 'Aprende a usar y desarrollar con Lightning Network.', 'Online'),
('Conferencia ABLA 2025', '2025-02-15', 'El evento más grande de Bitcoin en Latinoamérica.', 'Buenos Aires');

-- Create themes table
CREATE TABLE IF NOT EXISTS themes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    primary_color VARCHAR(20) NOT NULL,
    primary_hover VARCHAR(20) NOT NULL,
    primary_light VARCHAR(20) NOT NULL,
    dark_bg VARCHAR(20) NOT NULL,
    card_bg VARCHAR(20) NOT NULL,
    overlay_bg VARCHAR(20) NOT NULL,
    light_text VARCHAR(20) NOT NULL,
    muted_text VARCHAR(20) NOT NULL,
    border_color VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default themes
INSERT INTO themes (name, is_active, primary_color, primary_hover, primary_light, dark_bg, card_bg, overlay_bg, light_text, muted_text, border_color) VALUES
('Bitcoin Orange', TRUE, '#F7931A', '#FF9500', '#FEF4E8', '#090C14', '#13161F', '#00000099', '#FFFFFF', 'rgba(255, 255, 255, 0.7)', 'rgba(255, 255, 255, 0.1)'),
('Dark Blue', FALSE, '#3B82F6', '#2563EB', '#EFF6FF', '#0F172A', '#1E293B', '#00000099', '#FFFFFF', 'rgba(255, 255, 255, 0.7)', 'rgba(255, 255, 255, 0.1)'),
('Emerald', FALSE, '#10B981', '#059669', '#ECFDF5', '#0C1F17', '#132A1F', '#00000099', '#FFFFFF', 'rgba(255, 255, 255, 0.7)', 'rgba(255, 255, 255, 0.1)'),
('Purple', FALSE, '#8B5CF6', '#7C3AED', '#F5F3FF', '#1A103F', '#2E1A69', '#00000099', '#FFFFFF', 'rgba(255, 255, 255, 0.7)', 'rgba(255, 255, 255, 0.1)');