<?php
/**
 * Sets up the configuration file.
 *
 * This script is used to create a config.php file from the config-sample.php file.
 * It will prompt the user for database connection details and other configuration options.
 */

// Check if config.php already exists
if (file_exists('config.php')) {
    die('The file "config.php" already exists. If you need to reset any of the configuration items in this file, please delete it first. You may try running the installation again.');
}

// Check if config-sample.php exists
if (!file_exists('config-sample.php')) {
    die('Sorry, I need a config-sample.php file to work from. Please re-upload this file from your ABLA installation.');
}

// Load the config-sample.php file
$config_sample = file_get_contents('config-sample.php');

// Check if the form has been submitted
$step = isset($_GET['step']) ? (int) $_GET['step'] : 0;

// Function to generate a random string
function generate_random_string($length = 64) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_ []{}<>~`+=,.;:/?|';
    $string = '';
    for ($i = 0; $i < $length; $i++) {
        $string .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $string;
}

// Function to test the database connection
function test_db_connection($host, $user, $password, $name) {
    try {
        // Check if host is localhost and MySQL might be using a socket
        if ($host === 'localhost' || $host === '127.0.0.1') {
            // Try to connect with default socket
            $conn = @new mysqli($host, $user, $password);

            // If connection fails with "No such file or directory", MySQL might not be running
            if ($conn->connect_error && (
                strpos($conn->connect_error, 'No such file or directory') !== false ||
                strpos($conn->connect_error, 'Connection refused') !== false
            )) {
                return [
                    'success' => false,
                    'message' => "No se pudo conectar al servidor MySQL. Asegúrate de que MySQL esté instalado y en ejecución.",
                    'details' => "Error: " . $conn->connect_error,
                    'help' => [
                        "Verifica que el servidor MySQL esté en ejecución.",
                        "Si estás en un entorno local, inicia MySQL con XAMPP, MAMP o el servicio correspondiente.",
                        "Si estás en un servidor compartido, verifica las credenciales con tu proveedor de hosting."
                    ]
                ];
            }
        } else {
            // Try to connect to remote host
            $conn = @new mysqli($host, $user, $password);
        }

        // Check connection
        if ($conn->connect_error) {
            $error_message = "Connection failed: " . $conn->connect_error;
            $help = [];

            // Provide specific help based on error
            if (strpos($conn->connect_error, 'Access denied') !== false) {
                $help[] = "Verifica que el nombre de usuario y contraseña sean correctos.";
                $help[] = "Asegúrate de que el usuario tenga permisos para conectarse desde tu ubicación actual.";
            } elseif (strpos($conn->connect_error, 'Unknown host') !== false) {
                $help[] = "Verifica que el nombre del servidor sea correcto.";
                $help[] = "Comprueba tu conexión a internet si estás usando un servidor remoto.";
            }

            return [
                'success' => false,
                'message' => $error_message,
                'help' => $help
            ];
        }

        // Check if database exists
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$name'");

        if ($result && $result->num_rows > 0) {
            // Database exists, try to connect to it
            $conn->select_db($name);

            if ($conn->error) {
                return [
                    'success' => false,
                    'message' => "La base de datos existe pero no se pudo seleccionar: " . $conn->error
                ];
            }

            return [
                'success' => true,
                'message' => "Conexión exitosa. La base de datos ya existe."
            ];
        } else {
            // Database doesn't exist, try to create it
            if ($conn->query("CREATE DATABASE IF NOT EXISTS `$name`")) {
                return [
                    'success' => true,
                    'message' => "Conexión exitosa. Base de datos creada correctamente."
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "Conexión exitosa pero no se pudo crear la base de datos: " . $conn->error,
                    'help' => [
                        "Verifica que el usuario tenga permisos para crear bases de datos.",
                        "Intenta crear la base de datos manualmente a través de phpMyAdmin o la herramienta de administración de tu hosting."
                    ]
                ];
            }
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "Excepción: " . $e->getMessage()
        ];
    }
}

// Process form submission
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        // Database configuration
        $db_name = trim($_POST['db_name'] ?? '');
        $db_user = trim($_POST['db_user'] ?? '');
        $db_password = $_POST['db_password'] ?? '';
        $db_host = trim($_POST['db_host'] ?? 'localhost');

        // Test database connection
        $db_test = test_db_connection($db_host, $db_user, $db_password, $db_name);

        if ($db_test['success']) {
            // Connection successful, proceed to next step
            $success_message = $db_test['message'];
            $step = 2;
        } else {
            // Connection failed, show error
            $error_message = $db_test['message'];
            $step = 1;
        }
    } elseif ($step === 2) {
        // Site configuration
        $site_url = rtrim(trim($_POST['site_url'] ?? ''), '/');
        $table_prefix = trim($_POST['table_prefix'] ?? 'abla_');
        $debug_mode = isset($_POST['debug_mode']) && $_POST['debug_mode'] === 'on';

        // Database configuration (from previous step)
        $db_name = trim($_POST['db_name'] ?? '');
        $db_user = trim($_POST['db_user'] ?? '');
        $db_password = $_POST['db_password'] ?? '';
        $db_host = trim($_POST['db_host'] ?? 'localhost');

        // Generate security keys
        $auth_key = generate_random_string();
        $secure_auth_key = generate_random_string();
        $logged_in_key = generate_random_string();
        $nonce_key = generate_random_string();

        // Create config.php content
        $config_content = $config_sample;

        // Replace database settings
        $config_content = str_replace("define('DB_NAME', 'abla_db')", "define('DB_NAME', '$db_name')", $config_content);
        $config_content = str_replace("define('DB_USER', 'root')", "define('DB_USER', '$db_user')", $config_content);
        $config_content = str_replace("define('DB_PASSWORD', '')", "define('DB_PASSWORD', '$db_password')", $config_content);
        $config_content = str_replace("define('DB_HOST', 'localhost')", "define('DB_HOST', '$db_host')", $config_content);

        // Replace security keys
        $config_content = str_replace("define('AUTH_KEY',         'put your unique phrase here')", "define('AUTH_KEY',         '$auth_key')", $config_content);
        $config_content = str_replace("define('SECURE_AUTH_KEY',  'put your unique phrase here')", "define('SECURE_AUTH_KEY',  '$secure_auth_key')", $config_content);
        $config_content = str_replace("define('LOGGED_IN_KEY',    'put your unique phrase here')", "define('LOGGED_IN_KEY',    '$logged_in_key')", $config_content);
        $config_content = str_replace("define('NONCE_KEY',        'put your unique phrase here')", "define('NONCE_KEY',        '$nonce_key')", $config_content);

        // Replace site URL
        $config_content = str_replace("define('SITE_URL', 'http://localhost:8888')", "define('SITE_URL', '$site_url')", $config_content);

        // Replace debug mode
        $debug_value = $debug_mode ? 'true' : 'false';
        $config_content = str_replace("define('ABLA_DEBUG', false)", "define('ABLA_DEBUG', $debug_value)", $config_content);

        // Replace table prefix
        $config_content = str_replace("\$table_prefix = 'abla_'", "\$table_prefix = '$table_prefix'", $config_content);

        // Write config.php file
        if (file_put_contents('config.php', $config_content)) {
            $success_message = "Configuration file created successfully!";
            $step = 3;
        } else {
            $error_message = "Could not write config.php file. Please check file permissions.";
            $step = 2;
        }
    }
}

// HTML header
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABLA - Configuración</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <style>
        :root {
            --primary: #F7931A;
            --primary-hover: #FF9500;
            --primary-light: #FEF4E8;
            --dark-bg: #090C14;
            --card-bg: #13161F;
            --overlay-bg: #00000099;
            --light-text: #FFFFFF;
            --muted-text: rgba(255, 255, 255, 0.7);
            --border-color: rgba(255, 255, 255, 0.1);
            --success: #22c55e;
            --error: #ef4444;
        }

        body {
            background-color: var(--dark-bg);
            color: var(--light-text);
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: 0.75rem;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            color: var(--light-text);
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(247, 147, 26, 0.25);
        }

        .form-hint {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: var(--muted-text);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-error {
            background-color: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        .alert-success {
            background-color: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }

        .steps {
            display: flex;
            margin-bottom: 2rem;
        }

        .step {
            flex: 1;
            text-align: center;
            padding: 1rem;
            position: relative;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            width: 100%;
            height: 2px;
            background-color: var(--border-color);
            transform: translateY(-50%);
            z-index: -1;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background-color: var(--card-bg);
            border: 2px solid var(--border-color);
            margin-bottom: 0.5rem;
            font-weight: bold;
            z-index: 1;
        }

        .step.active .step-number {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }

        .step.completed .step-number {
            background-color: var(--success);
            border-color: var(--success);
            color: white;
        }

        .step-label {
            font-size: 0.875rem;
            color: var(--muted-text);
        }

        .step.active .step-label {
            color: var(--light-text);
            font-weight: 500;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 1.25rem;
            height: 1.25rem;
            accent-color: var(--primary);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <i class="ph ph-bitcoin-circle text-orange-500 text-4xl"></i>
            <div>
                <h1>ABLA - Configuración</h1>
                <p class="text-muted">Asistente de configuración</p>
            </div>
        </div>

        <div class="steps">
            <div class="step <?php echo $step === 0 || $step === 1 ? 'active' : ($step > 1 ? 'completed' : ''); ?>">
                <div class="step-number">1</div>
                <div class="step-label">Base de datos</div>
            </div>
            <div class="step <?php echo $step === 2 ? 'active' : ($step > 2 ? 'completed' : ''); ?>">
                <div class="step-number">2</div>
                <div class="step-label">Configuración del sitio</div>
            </div>
            <div class="step <?php echo $step === 3 ? 'active' : ''; ?>">
                <div class="step-number">3</div>
                <div class="step-label">Finalizar</div>
            </div>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-error">
            <p class="font-medium"><?php echo $error_message; ?></p>

            <?php if (isset($db_test) && isset($db_test['details'])): ?>
            <p class="mt-2 text-sm"><?php echo $db_test['details']; ?></p>
            <?php endif; ?>

            <?php if (isset($db_test) && isset($db_test['help']) && !empty($db_test['help'])): ?>
            <div class="mt-3 pt-3 border-t border-red-500/20">
                <p class="font-medium text-sm mb-1">Sugerencias:</p>
                <ul class="list-disc list-inside text-sm space-y-1">
                    <?php foreach ($db_test['help'] as $help_item): ?>
                    <li><?php echo $help_item; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <p><?php echo $success_message; ?></p>
        </div>
        <?php endif; ?>

        <div class="card">
            <?php if ($step === 0 || $step === 1): ?>
                <h2 class="text-xl font-bold mb-4">Configuración de la base de datos</h2>
                <p class="mb-4">Ingresa la información de conexión a tu base de datos MySQL. Esta información es necesaria para que ABLA pueda almacenar y recuperar datos.</p>

                <div class="bg-blue-900/20 border border-blue-500/30 p-4 rounded-lg mb-6">
                    <div class="flex items-start gap-3">
                        <i class="ph ph-info text-blue-500 text-xl mt-0.5"></i>
                        <div>
                            <p class="font-medium mb-2">Información importante</p>
                            <ul class="list-disc list-inside space-y-1 text-sm text-gray-300">
                                <li>Asegúrate de que el servidor MySQL esté en ejecución antes de continuar.</li>
                                <li>Si estás en un entorno local, necesitas tener XAMPP, MAMP, o un servidor MySQL instalado.</li>
                                <li>Si estás en un servidor compartido, solicita esta información a tu proveedor de hosting.</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <form method="POST" action="?step=1">
                    <div class="form-group">
                        <label for="db_host" class="form-label">Servidor de la base de datos</label>
                        <input type="text" id="db_host" name="db_host" class="form-input" value="localhost" required>
                        <p class="form-hint">Generalmente es "localhost"</p>
                    </div>

                    <div class="form-group">
                        <label for="db_name" class="form-label">Nombre de la base de datos</label>
                        <input type="text" id="db_name" name="db_name" class="form-input" value="abla_db" required>
                        <p class="form-hint">El nombre de la base de datos que deseas utilizar con ABLA</p>
                    </div>

                    <div class="form-group">
                        <label for="db_user" class="form-label">Usuario de la base de datos</label>
                        <input type="text" id="db_user" name="db_user" class="form-input" value="root" required>
                        <p class="form-hint">Tu nombre de usuario de MySQL</p>
                    </div>

                    <div class="form-group">
                        <label for="db_password" class="form-label">Contraseña de la base de datos</label>
                        <input type="password" id="db_password" name="db_password" class="form-input">
                        <p class="form-hint">Tu contraseña de MySQL</p>
                    </div>

                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">
                            Continuar
                            <i class="ph ph-arrow-right"></i>
                        </button>
                    </div>
                </form>

                <div class="mt-8 pt-6 border-t border-gray-700">
                    <h3 class="text-lg font-medium mb-3">Solución de problemas comunes</h3>

                    <div class="space-y-4">
                        <div>
                            <h4 class="font-medium text-orange-500">Error "No such file or directory"</h4>
                            <p class="text-sm text-gray-300 mt-1">Este error indica que el servidor MySQL no está en ejecución o no se puede encontrar el socket. Asegúrate de que MySQL esté instalado y en ejecución.</p>
                            <div class="mt-2 bg-gray-800 p-3 rounded-lg">
                                <p class="text-sm font-medium mb-1">Soluciones:</p>
                                <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                    <li>Si usas XAMPP, abre el panel de control y asegúrate de que MySQL esté iniciado.</li>
                                    <li>Si usas MAMP, verifica que el servidor esté en ejecución.</li>
                                    <li>En macOS, puedes iniciar MySQL con <code>brew services start mysql</code> si lo instalaste con Homebrew.</li>
                                    <li>En Linux, usa <code>sudo service mysql start</code> o <code>sudo systemctl start mysql</code>.</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-orange-500">Error "Access denied"</h4>
                            <p class="text-sm text-gray-300 mt-1">Este error indica que las credenciales de acceso (usuario/contraseña) son incorrectas o que el usuario no tiene permisos suficientes.</p>
                            <div class="mt-2 bg-gray-800 p-3 rounded-lg">
                                <p class="text-sm font-medium mb-1">Soluciones:</p>
                                <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                    <li>Verifica que el nombre de usuario y contraseña sean correctos.</li>
                                    <li>Si estás en un entorno local, prueba con usuario "root" y contraseña en blanco (configuración por defecto).</li>
                                    <li>Si has olvidado la contraseña, consulta la documentación de MySQL para restablecerla.</li>
                                </ul>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-orange-500">Error "Unknown database"</h4>
                            <p class="text-sm text-gray-300 mt-1">Este error indica que la base de datos especificada no existe.</p>
                            <div class="mt-2 bg-gray-800 p-3 rounded-lg">
                                <p class="text-sm font-medium mb-1">Soluciones:</p>
                                <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                    <li>Verifica que el nombre de la base de datos sea correcto.</li>
                                    <li>El asistente intentará crear la base de datos si no existe, pero el usuario debe tener permisos para ello.</li>
                                    <li>Puedes crear la base de datos manualmente usando phpMyAdmin o la línea de comandos de MySQL.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif ($step === 2): ?>
                <h2 class="text-xl font-bold mb-4">Configuración del sitio</h2>
                <p class="mb-4">Ahora vamos a configurar algunos detalles adicionales de tu sitio.</p>

                <form method="POST" action="?step=2">
                    <!-- Hidden fields to pass database info to next step -->
                    <input type="hidden" name="db_host" value="<?php echo htmlspecialchars($db_host); ?>">
                    <input type="hidden" name="db_name" value="<?php echo htmlspecialchars($db_name); ?>">
                    <input type="hidden" name="db_user" value="<?php echo htmlspecialchars($db_user); ?>">
                    <input type="hidden" name="db_password" value="<?php echo htmlspecialchars($db_password); ?>">

                    <div class="form-group">
                        <label for="site_url" class="form-label">URL del sitio</label>
                        <input type="url" id="site_url" name="site_url" class="form-input" value="<?php echo htmlspecialchars(isset($_SERVER['REQUEST_SCHEME']) ? $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] : 'http://localhost:8888'); ?>" required>
                        <p class="form-hint">La URL completa de tu sitio, sin barra al final</p>
                    </div>

                    <div class="form-group">
                        <label for="table_prefix" class="form-label">Prefijo de tablas</label>
                        <input type="text" id="table_prefix" name="table_prefix" class="form-input" value="abla_" required>
                        <p class="form-hint">Si deseas ejecutar múltiples instalaciones de ABLA en una sola base de datos, cambia esto</p>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="debug_mode" name="debug_mode">
                            <label for="debug_mode" class="form-label" style="margin-bottom: 0;">Modo de depuración</label>
                        </div>
                        <p class="form-hint">Activa esto solo en entornos de desarrollo</p>
                    </div>

                    <div class="text-right">
                        <button type="submit" class="btn btn-primary">
                            Crear archivo de configuración
                            <i class="ph ph-check"></i>
                        </button>
                    </div>
                </form>
            <?php elseif ($step === 3): ?>
                <h2 class="text-xl font-bold mb-4">¡Configuración completada!</h2>
                <p class="mb-4">El archivo de configuración ha sido creado correctamente.</p>

                <div class="bg-gray-800 p-4 rounded-lg mb-4">
                    <p class="mb-2">Se ha creado el archivo <code>config.php</code> con la siguiente configuración:</p>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>Base de datos: <code><?php echo htmlspecialchars($db_name); ?></code></li>
                        <li>Usuario: <code><?php echo htmlspecialchars($db_user); ?></code></li>
                        <li>Servidor: <code><?php echo htmlspecialchars($db_host); ?></code></li>
                        <li>URL del sitio: <code><?php echo htmlspecialchars($site_url); ?></code></li>
                        <li>Prefijo de tablas: <code><?php echo htmlspecialchars($table_prefix); ?></code></li>
                        <li>Modo de depuración: <code><?php echo $debug_mode ? 'Activado' : 'Desactivado'; ?></code></li>
                    </ul>
                </div>

                <p class="mb-4">Ahora puedes continuar con la instalación de ABLA.</p>

                <div class="text-right">
                    <a href="index.php" class="btn btn-primary">
                        Continuar a la instalación
                        <i class="ph ph-arrow-right"></i>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
