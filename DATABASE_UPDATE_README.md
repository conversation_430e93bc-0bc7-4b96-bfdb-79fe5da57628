# Database Update Utility - ABLA

This utility helps you safely apply database schema changes to existing production databases without losing data.

## Files Created

1. **`dbutil.php`** - Main database utility script
2. **`dbutil_config.php`** - Configuration and access control
3. **`update_db.php`** - Secure web interface with authentication
4. **`DATABASE_UPDATE_README.md`** - This documentation

## How to Use

### Option 1: Secure Web Interface (Recommended)

1. Upload all files to your server
2. Navigate to: `https://yourdomain.com/update_db.php`
3. Enter the password: `abla_db_2024` (change this in the file)
4. Follow the on-screen instructions

### Option 2: Direct Access (Development Only)

1. Navigate to: `https://yourdomain.com/dbutil.php?allow=true`
2. This only works from localhost/development environments

### Option 3: Command Line

```bash
php dbutil.php
```

## What This Utility Does

### Current Migrations Available

- **v1.0.0**: Initial database setup (users, content, initiatives, events, contacts, themes)
- **v1.1.0**: Adds `messages` table for contact form submissions
- **v1.2.0**: Adds `db_migrations` table for version tracking

### Features

- ✅ **Safe Updates**: Only applies missing changes, won't overwrite existing data
- ✅ **Version Tracking**: Keeps track of applied migrations
- ✅ **Rollback Protection**: Won't apply migrations that could cause data loss
- ✅ **Detailed Logging**: Shows exactly what changes are being made
- ✅ **Status Checking**: Displays current database state and table counts
- ✅ **Error Handling**: Stops on errors to prevent corruption

## Security Features

- **IP Restriction**: Only works from allowed IP addresses
- **Password Protection**: Requires authentication for web access
- **Access Control**: Can be completely disabled via configuration
- **Development Mode**: Automatically detects development environments

## Important Notes

### Before Running

1. **Backup Your Database**: Always backup before running migrations
2. **Test Environment**: Test on a copy of your production database first
3. **Maintenance Mode**: Consider putting your site in maintenance mode during updates

### After Running

1. **Verify Results**: Check that all tables and data are correct
2. **Test Functionality**: Ensure all website features work properly
3. **Disable Utility**: Set `DB_UTIL_ALLOWED` to `false` in `dbutil_config.php`

## Configuration

### Changing Access Password

Edit `update_db.php` and change:
```php
$auth_password = 'your_new_password_here';
```

### Adding Allowed IP Addresses

Edit `dbutil_config.php` and add your IPs:
```php
$allowed_ips = [
    '127.0.0.1',
    '::1',
    'localhost',
    '*************', // Your IP here
];
```

### Disabling the Utility

Edit `dbutil_config.php`:
```php
define('DB_UTIL_ALLOWED', false);
```

## Troubleshooting

### Common Issues

1. **"Access denied"**: Check IP restrictions and password
2. **"Connection failed"**: Verify database credentials in `includes/db.php`
3. **"Table already exists"**: This is normal, the utility skips existing tables
4. **"Migration failed"**: Check database permissions and error messages

### Getting Help

1. Check the detailed error messages in the utility output
2. Verify your database connection settings
3. Ensure your database user has CREATE, ALTER, and INSERT permissions
4. Check server error logs for additional details

## Database Schema Changes

### Messages Table (v1.1.0)

The new `messages` table stores contact form submissions with:
- Basic contact info (name, email, subject, message)
- Social media handles (Instagram, Twitter)
- Status tracking (unread, read, replied)
- Metadata (IP address, user agent, timestamps)

### Migration Tracking (v1.2.0)

The `db_migrations` table tracks which updates have been applied:
- Version numbers
- Descriptions of changes
- Application timestamps

## Best Practices

1. **Always backup** before running migrations
2. **Test first** on a development/staging environment
3. **Run during low traffic** periods
4. **Monitor** the process and check for errors
5. **Verify** all functionality after completion
6. **Disable** the utility after use

## Support

If you encounter issues:
1. Check this documentation
2. Review error messages carefully
3. Verify database permissions
4. Test on a development environment first

---

**⚠️ Security Warning**: Always disable this utility after use by setting `DB_UTIL_ALLOWED` to `false` in the configuration file.
