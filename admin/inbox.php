<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /?route=admin');
    exit;
}

// Database connection
$db = connectDB();
if (is_array($db)) {
    // Connection error
    $error = "Error de conexión a la base de datos: " . $db['error'];
    include 'admin/db_init_error.php';
    exit;
}

// Handle actions
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['mark_read']) && isset($_POST['message_id'])) {
        $message_id = intval($_POST['message_id']);
        $stmt = $db->prepare("UPDATE messages SET status = 'read' WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $message_id);
            if ($stmt->execute()) {
                $success = true;
                $message = "Mensaje marcado como leído.";
            }
            $stmt->close();
        }
    } elseif (isset($_POST['mark_replied']) && isset($_POST['message_id'])) {
        $message_id = intval($_POST['message_id']);
        $stmt = $db->prepare("UPDATE messages SET status = 'replied' WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $message_id);
            if ($stmt->execute()) {
                $success = true;
                $message = "Mensaje marcado como respondido.";
            }
            $stmt->close();
        }
    } elseif (isset($_POST['delete_message']) && isset($_POST['message_id'])) {
        $message_id = intval($_POST['message_id']);
        $stmt = $db->prepare("DELETE FROM messages WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param("i", $message_id);
            if ($stmt->execute()) {
                $success = true;
                $message = "Mensaje eliminado.";
            }
            $stmt->close();
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];
$param_types = '';

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_query = "SELECT COUNT(*) as total FROM messages $where_clause";
$count_stmt = $db->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_messages = $count_stmt->get_result()->fetch_assoc()['total'];
$count_stmt->close();

$total_pages = ceil($total_messages / $per_page);

// Get messages
$query = "SELECT * FROM messages $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?";
$params[] = $per_page;
$params[] = $offset;
$param_types .= 'ii';

$stmt = $db->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$messages_result = $stmt->get_result();
$messages = [];
while ($row = $messages_result->fetch_assoc()) {
    $messages[] = $row;
}
$stmt->close();

// Get message counts by status
$stats_query = "SELECT status, COUNT(*) as count FROM messages GROUP BY status";
$stats_result = $db->query($stats_query);
$stats = ['unread' => 0, 'read' => 0, 'replied' => 0];
while ($row = $stats_result->fetch_assoc()) {
    $stats[$row['status']] = $row['count'];
}

// Function to format time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'hace ' . $time . ' segundos';
    if ($time < 3600) return 'hace ' . floor($time/60) . ' minutos';
    if ($time < 86400) return 'hace ' . floor($time/3600) . ' horas';
    if ($time < 2592000) return 'hace ' . floor($time/86400) . ' días';
    if ($time < 31536000) return 'hace ' . floor($time/2592000) . ' meses';
    return 'hace ' . floor($time/31536000) . ' años';
}

// Function to get status badge
function getStatusBadge($status) {
    switch ($status) {
        case 'unread':
            return '<span class="px-2 py-1 text-xs font-medium bg-red-900 text-red-300 rounded-full">Sin leer</span>';
        case 'read':
            return '<span class="px-2 py-1 text-xs font-medium bg-yellow-900 text-yellow-300 rounded-full">Leído</span>';
        case 'replied':
            return '<span class="px-2 py-1 text-xs font-medium bg-green-900 text-green-300 rounded-full">Respondido</span>';
        default:
            return '<span class="px-2 py-1 text-xs font-medium bg-gray-900 text-gray-300 rounded-full">Desconocido</span>';
    }
}
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-white">Bandeja de Entrada</h1>
            <a href="/?route=admin" class="btn btn-secondary">
                <i class="ph ph-arrow-left"></i> Volver al Dashboard
            </a>
        </div>
        
        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?> mb-8">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Total</p>
                        <p class="text-2xl font-bold text-white"><?php echo array_sum($stats); ?></p>
                    </div>
                    <div class="bg-blue-500 rounded-full p-3">
                        <i class="ph ph-envelope text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Sin leer</p>
                        <p class="text-2xl font-bold text-white"><?php echo $stats['unread']; ?></p>
                    </div>
                    <div class="bg-red-500 rounded-full p-3">
                        <i class="ph ph-envelope-simple text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Leídos</p>
                        <p class="text-2xl font-bold text-white"><?php echo $stats['read']; ?></p>
                    </div>
                    <div class="bg-yellow-500 rounded-full p-3">
                        <i class="ph ph-envelope-open text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Respondidos</p>
                        <p class="text-2xl font-bold text-white"><?php echo $stats['replied']; ?></p>
                    </div>
                    <div class="bg-green-500 rounded-full p-3">
                        <i class="ph ph-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="card mb-8">
            <div class="p-6">
                <form method="GET" action="/?route=admin&page=inbox" class="flex flex-wrap gap-4 items-end">
                    <div class="flex-1 min-w-64">
                        <label for="search" class="block text-sm font-medium text-gray-300 mb-1">Buscar</label>
                        <input 
                            type="text" 
                            id="search" 
                            name="search" 
                            value="<?php echo htmlspecialchars($search); ?>"
                            placeholder="Buscar por nombre, email, asunto o mensaje..."
                            class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                        >
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-1">Estado</label>
                        <select 
                            id="status" 
                            name="status" 
                            class="p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                        >
                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>Todos</option>
                            <option value="unread" <?php echo $status_filter === 'unread' ? 'selected' : ''; ?>>Sin leer</option>
                            <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Leídos</option>
                            <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Respondidos</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="ph ph-magnifying-glass"></i> Buscar
                    </button>
                    
                    <a href="/?route=admin&page=inbox" class="btn btn-secondary">
                        <i class="ph ph-x"></i> Limpiar
                    </a>
                </form>
            </div>
        </div>
        
        <!-- Messages List -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-bold text-white flex items-center gap-2">
                    <i class="ph ph-envelope text-orange-500"></i> 
                    Mensajes 
                    <span class="text-sm font-normal text-gray-400">(<?php echo $total_messages; ?> total)</span>
                </h2>
            </div>
            
            <div class="overflow-x-auto">
                <?php if (empty($messages)): ?>
                <div class="p-8 text-center">
                    <i class="ph ph-envelope-simple text-gray-600 text-6xl mb-4"></i>
                    <p class="text-gray-400 text-lg">No hay mensajes que mostrar.</p>
                    <?php if (!empty($search) || $status_filter !== 'all'): ?>
                    <p class="text-gray-500 text-sm mt-2">Intenta ajustar los filtros de búsqueda.</p>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <table class="w-full">
                    <thead class="bg-gray-800">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Remitente</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Asunto</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Estado</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Fecha</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">
                        <?php foreach ($messages as $msg): ?>
                        <tr class="hover:bg-gray-800 <?php echo $msg['status'] === 'unread' ? 'bg-gray-800/50' : ''; ?>">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-white"><?php echo htmlspecialchars($msg['name']); ?></div>
                                    <div class="text-sm text-gray-400"><?php echo htmlspecialchars($msg['email']); ?></div>
                                    <?php if (!empty($msg['instagram']) || !empty($msg['twitter'])): ?>
                                    <div class="flex gap-2 mt-1">
                                        <?php if (!empty($msg['instagram'])): ?>
                                        <span class="text-xs text-gray-500">
                                            <i class="ph ph-instagram-logo"></i> @<?php echo htmlspecialchars($msg['instagram']); ?>
                                        </span>
                                        <?php endif; ?>
                                        <?php if (!empty($msg['twitter'])): ?>
                                        <span class="text-xs text-gray-500">
                                            <i class="ph ph-twitter-logo"></i> @<?php echo htmlspecialchars($msg['twitter']); ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-white"><?php echo htmlspecialchars($msg['subject']); ?></div>
                                <div class="text-sm text-gray-400 truncate max-w-xs"><?php echo htmlspecialchars(substr($msg['message'], 0, 100)) . (strlen($msg['message']) > 100 ? '...' : ''); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <?php echo getStatusBadge($msg['status']); ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-400">
                                <div><?php echo date('d/m/Y H:i', strtotime($msg['created_at'])); ?></div>
                                <div class="text-xs"><?php echo timeAgo($msg['created_at']); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex gap-2">
                                    <button 
                                        onclick="viewMessage(<?php echo $msg['id']; ?>)"
                                        class="text-blue-400 hover:text-blue-300"
                                        title="Ver mensaje"
                                    >
                                        <i class="ph ph-eye"></i>
                                    </button>
                                    
                                    <?php if ($msg['status'] === 'unread'): ?>
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                        <button 
                                            type="submit" 
                                            name="mark_read"
                                            class="text-yellow-400 hover:text-yellow-300"
                                            title="Marcar como leído"
                                        >
                                            <i class="ph ph-envelope-open"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    
                                    <?php if ($msg['status'] !== 'replied'): ?>
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                        <button 
                                            type="submit" 
                                            name="mark_replied"
                                            class="text-green-400 hover:text-green-300"
                                            title="Marcar como respondido"
                                        >
                                            <i class="ph ph-check-circle"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    
                                    <form method="POST" class="inline" onsubmit="return confirm('¿Estás seguro de que quieres eliminar este mensaje?')">
                                        <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                        <button 
                                            type="submit" 
                                            name="delete_message"
                                            class="text-red-400 hover:text-red-300"
                                            title="Eliminar mensaje"
                                        >
                                            <i class="ph ph-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php endif; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 border-t border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        Mostrando <?php echo $offset + 1; ?> a <?php echo min($offset + $per_page, $total_messages); ?> de <?php echo $total_messages; ?> mensajes
                    </div>
                    
                    <div class="flex gap-2">
                        <?php if ($page > 1): ?>
                        <a href="/?route=admin&page=inbox&page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" 
                           class="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-600">
                            Anterior
                        </a>
                        <?php endif; ?>
                        
                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="/?route=admin&page=inbox&page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" 
                           class="px-3 py-1 <?php echo $i === $page ? 'bg-orange-500 text-white' : 'bg-gray-700 text-white hover:bg-gray-600'; ?> rounded">
                            <?php echo $i; ?>
                        </a>
                        <?php endfor; ?>
                        
                        <?php if ($page < $total_pages): ?>
                        <a href="/?route=admin&page=inbox&page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>" 
                           class="px-3 py-1 bg-gray-700 text-white rounded hover:bg-gray-600">
                            Siguiente
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Message Modal -->
<div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-gray-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-white">Detalle del Mensaje</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                        <i class="ph ph-x text-xl"></i>
                    </button>
                </div>
                
                <div id="messageContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewMessage(messageId) {
    // Show modal
    document.getElementById('messageModal').classList.remove('hidden');
    
    // Load message content
    fetch(`/?route=admin&page=inbox&action=view&id=${messageId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('messageContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('messageContent').innerHTML = '<p class="text-red-400">Error al cargar el mensaje.</p>';
        });
}

function closeModal() {
    document.getElementById('messageModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('messageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
