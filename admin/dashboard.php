<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /?route=admin');
    exit;
}

// Process logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_unset();
    session_destroy();
    header('Location: /?route=admin');
    exit;
}

// Database connection
$db = connectDB();

// Process form submissions
$message = '';
$success = false;

// Check if a specific admin page is requested
$admin_page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Include the requested page
if ($admin_page !== 'dashboard') {
    $page_file = __DIR__ . '/' . $admin_page . '.php';
    if (file_exists($page_file)) {
        include $page_file;
        exit;
    }
}

// Handle content update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_content'])) {
    $section = $_POST['section'] ?? '';
    $title = $_POST['title'] ?? '';
    $subtitle = $_POST['subtitle'] ?? '';
    $content = $_POST['content'] ?? '';
    $id = $_POST['id'] ?? '';

    if (empty($section) || empty($title)) {
        $message = 'La sección y el título son obligatorios.';
    } else {
        if (!empty($id)) {
            // Update existing content
            $stmt = $db->prepare("UPDATE content SET title = ?, subtitle = ?, content = ? WHERE id = ?");
            $stmt->bind_param("sssi", $title, $subtitle, $content, $id);
        } else {
            // Insert new content
            $stmt = $db->prepare("INSERT INTO content (section, title, subtitle, content) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $section, $title, $subtitle, $content);
        }

        if ($stmt->execute()) {
            $success = true;
            $message = 'Contenido actualizado correctamente.';
        } else {
            $message = 'Error al actualizar el contenido: ' . $db->error;
        }
        $stmt->close();
    }
}

// Handle initiative update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_initiative'])) {
    $name = $_POST['name'] ?? '';
    $instagram_user = $_POST['instagram_user'] ?? '';
    $instagram_link = $_POST['instagram_link'] ?? '';
    $description = $_POST['description'] ?? '';
    $id = $_POST['id'] ?? '';

    if (empty($name) || empty($instagram_user)) {
        $message = 'El nombre y el usuario de Instagram son obligatorios.';
    } else {
        if (!empty($id)) {
            // Update existing initiative
            $stmt = $db->prepare("UPDATE initiatives SET name = ?, instagram_user = ?, instagram_link = ?, description = ? WHERE id = ?");
            $stmt->bind_param("ssssi", $name, $instagram_user, $instagram_link, $description, $id);
        } else {
            // Insert new initiative
            $stmt = $db->prepare("INSERT INTO initiatives (name, instagram_user, instagram_link, description) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $name, $instagram_user, $instagram_link, $description);
        }

        if ($stmt->execute()) {
            $success = true;
            $message = 'Iniciativa actualizada correctamente.';
        } else {
            $message = 'Error al actualizar la iniciativa: ' . $db->error;
        }
        $stmt->close();
    }
}

// Handle delete
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['type']) && isset($_GET['id'])) {
    $type = $_GET['type'];
    $id = (int)$_GET['id'];

    if ($type === 'content') {
        $stmt = $db->prepare("DELETE FROM content WHERE id = ?");
    } elseif ($type === 'initiative') {
        $stmt = $db->prepare("DELETE FROM initiatives WHERE id = ?");
    }

    if (isset($stmt)) {
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success = true;
            $message = 'Elemento eliminado correctamente.';
        } else {
            $message = 'Error al eliminar el elemento: ' . $db->error;
        }
        $stmt->close();
    }
}

// Check if tables exist
$tables_exist = true;
$required_tables = ['content', 'initiatives', 'events', 'users', 'themes'];
$missing_tables = [];

foreach ($required_tables as $table) {
    try {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows === 0) {
            $missing_tables[] = $table;
        }
    } catch (Exception $e) {
        $missing_tables[] = $table;
    }
}

// Only show database initialization if critical tables are missing
$critical_tables = ['content', 'initiatives', 'users'];
$critical_missing = array_intersect($critical_tables, $missing_tables);

if (!empty($critical_missing)) {
    $tables_exist = false;
    $message = 'Faltan tablas críticas en la base de datos: ' . implode(', ', $critical_missing) . '. Por favor inicialice la base de datos.';
    $success = false;
} elseif (!empty($missing_tables)) {
    // Show warning for non-critical missing tables
    $message = 'Algunas tablas opcionales no existen: ' . implode(', ', $missing_tables) . '. Considere ejecutar la inicialización de base de datos para obtener todas las funcionalidades.';
    $success = false;
}

// Get content data
$content_data = [];
if ($tables_exist) {
    try {
        $result = $db->query("SELECT * FROM content ORDER BY section, id DESC");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $content_data[] = $row;
            }
        }
    } catch (Exception $e) {
        // Table might exist but have issues
        $message = 'Error al obtener contenido: ' . $e->getMessage();
        $success = false;
    }
}

// Get initiatives data
$initiatives_data = [];
if ($tables_exist) {
    try {
        $result = $db->query("SELECT * FROM initiatives ORDER BY id DESC");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $initiatives_data[] = $row;
            }
        }
    } catch (Exception $e) {
        // Table might exist but have issues
        if (empty($message)) {
            $message = 'Error al obtener iniciativas: ' . $e->getMessage();
            $success = false;
        }
    }
}
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white">Panel de Administración</h1>
                <div class="flex mt-2 space-x-4">
                    <a href="/?route=admin" class="text-gray-300 hover:text-white <?php echo $admin_page === 'dashboard' ? 'border-b-2 border-orange-500' : ''; ?>">
                        Dashboard
                    </a>
                    <a href="/?route=admin&page=themes" class="text-gray-300 hover:text-white <?php echo $admin_page === 'themes' ? 'border-b-2 border-orange-500' : ''; ?>">
                        Temas
                    </a>
                    <a href="/?route=admin&page=inbox" class="text-gray-300 hover:text-white <?php echo $admin_page === 'inbox' ? 'border-b-2 border-orange-500' : ''; ?> relative">
                        Mensajes
                        <?php
                        // Get unread message count
                        $unread_count = 0;
                        if (!is_array($db)) {
                            $unread_result = $db->query("SELECT COUNT(*) as count FROM messages WHERE status = 'unread'");
                            if ($unread_result) {
                                $unread_count = $unread_result->fetch_assoc()['count'];
                            }
                        }
                        if ($unread_count > 0):
                        ?>
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                            <?php echo $unread_count > 99 ? '99+' : $unread_count; ?>
                        </span>
                        <?php endif; ?>
                    </a>
                    <div class="relative group">
                        <a href="/?route=admin&page=db_init" class="text-gray-300 hover:text-white <?php echo in_array($admin_page, ['db_init', 'db_export']) ? 'border-b-2 border-orange-500' : ''; ?>">
                            Base de Datos
                        </a>
                        <div class="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                            <a href="/?route=admin&page=db_init" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                <i class="ph ph-database-plus mr-2"></i> Inicializar DB
                            </a>
                            <a href="/?route=admin&page=db_export" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                <i class="ph ph-database-export mr-2"></i> Exportar DB
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <span class="text-gray-400 mr-4">Bienvenido, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="/?route=admin&action=logout" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">Cerrar sesión</a>
            </div>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?> mb-8">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
                <?php if (!$tables_exist): ?>
                <div class="mt-2">
                    <a href="/?route=admin&page=db_init" class="btn btn-primary btn-sm">
                        <i class="ph ph-database-plus"></i> Inicializar Base de Datos
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!$tables_exist): ?>
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                    <i class="ph ph-database text-orange-500"></i> Base de Datos
                </h2>
            </div>

            <div class="p-6">
                <div class="bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg mb-6">
                    <div class="flex items-start gap-3">
                        <i class="ph ph-warning-circle text-orange-500 text-xl mt-0.5"></i>
                        <div>
                            <p class="font-medium mb-2">Tablas de base de datos no encontradas</p>
                            <p class="text-sm text-gray-300">Es necesario inicializar la base de datos para el correcto funcionamiento del sitio.</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <a href="/?route=admin&page=db_init" class="btn btn-primary">
                        <i class="ph ph-database-plus"></i> Inicializar Base de Datos
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions (Always show database management) -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Gestión de Base de Datos</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/?route=admin&page=db_init" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-database text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Inicializar DB</span>
                    <span class="text-xs text-gray-400">Crear tablas</span>
                </a>

                <a href="/update_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-wrench text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Actualizar DB</span>
                    <span class="text-xs text-gray-400">Migrar cambios</span>
                </a>

                <a href="/debug_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-bug text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Debug DB</span>
                    <span class="text-xs text-gray-400">Diagnosticar</span>
                </a>

                <a href="/check_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-check-circle text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Verificar DB</span>
                    <span class="text-xs text-gray-400">Estado rápido</span>
                </a>
            </div>
        </div>

        <?php if (empty($critical_missing)): ?>
        <!-- Quick Actions -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Acciones Rápidas</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/?route=admin&page=inbox" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-envelope text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Mensajes</span>
                    <?php if ($unread_count > 0): ?>
                    <span class="text-xs text-orange-400"><?php echo $unread_count; ?> sin leer</span>
                    <?php endif; ?>
                </a>

                <a href="/?route=admin&page=themes" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-palette text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Temas</span>
                    <span class="text-xs text-gray-400">Personalizar</span>
                </a>

                <a href="/?route=admin&page=db_init" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-database text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Base de Datos</span>
                    <span class="text-xs text-gray-400">Gestionar</span>
                </a>

                <a href="/" target="_blank" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-globe text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Ver Sitio</span>
                    <span class="text-xs text-gray-400">Nueva pestaña</span>
                </a>
            </div>
        </div>

        <!-- Dashboard Widgets -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <!-- Quick Stats -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-2xl font-bold text-white mb-6">Estadísticas Rápidas</h2>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Iniciativas</p>
                                <p class="text-2xl font-bold text-white"><?php echo count($initiatives_data); ?></p>
                            </div>
                            <div class="bg-orange-500 rounded-full p-3">
                                <i class="ph ph-handshake text-white text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Contenidos</p>
                                <p class="text-2xl font-bold text-white"><?php echo count($content_data); ?></p>
                            </div>
                            <div class="bg-orange-500 rounded-full p-3">
                                <i class="ph ph-article text-white text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Info -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Base de Datos</h2>
                    <div class="flex gap-2">
                        <a href="/?route=admin&page=db_init" class="text-orange-500 hover:text-orange-400 text-sm flex items-center gap-1">
                            <i class="ph ph-database-plus"></i> Gestionar
                        </a>
                        <a href="/?route=admin&page=db_export" class="text-blue-500 hover:text-blue-400 text-sm flex items-center gap-1">
                            <i class="ph ph-database-export"></i> Exportar
                        </a>
                    </div>
                </div>

                <?php
                // Get database info
                $db_info = [];
                $tables_count = 0;
                $db_size = 0;

                // Get database name
                $db_name = '';
                $result = $db->query("SELECT DATABASE()");
                if ($result && $row = $result->fetch_row()) {
                    $db_name = $row[0];
                }

                // Get all existing tables
                $existing_tables = [];
                $result = $db->query("SHOW TABLES");
                if ($result) {
                    $tables_count = $result->num_rows;
                    while ($row = $result->fetch_row()) {
                        $existing_tables[] = $row[0];
                    }

                    // Get database size
                    $size_result = $db->query("SELECT SUM(data_length + index_length) AS size FROM information_schema.TABLES WHERE table_schema = '$db_name'");
                    if ($size_result && $size_row = $size_result->fetch_assoc()) {
                        $db_size = $size_row['size'];
                    }
                }

                // Format database size
                function formatSize($bytes) {
                    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
                    $i = 0;
                    while ($bytes >= 1024 && $i < count($units) - 1) {
                        $bytes /= 1024;
                        $i++;
                    }
                    return round($bytes, 2) . ' ' . $units[$i];
                }

                // All expected tables
                $all_expected_tables = ['content', 'initiatives', 'events', 'users', 'themes', 'messages', 'contacts'];
                ?>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Base de datos:</p>
                        <p class="text-white font-mono"><?php echo htmlspecialchars($db_name); ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Tablas:</p>
                        <p class="text-white font-mono"><?php echo $tables_count; ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Tamaño:</p>
                        <p class="text-white font-mono"><?php echo formatSize($db_size); ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Estado:</p>
                        <p class="text-green-500 flex items-center gap-1">
                            <i class="ph ph-check-circle"></i> Conectado
                        </p>
                    </div>

                    <!-- Table Status -->
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <p class="text-gray-400 text-sm mb-2">Estado de tablas:</p>
                        <div class="grid grid-cols-2 gap-1 text-xs">
                            <?php foreach ($all_expected_tables as $table): ?>
                            <div class="flex items-center gap-1">
                                <?php if (in_array($table, $existing_tables)): ?>
                                <i class="ph ph-check-circle text-green-500"></i>
                                <span class="text-green-400"><?php echo $table; ?></span>
                                <?php else: ?>
                                <i class="ph ph-x-circle text-red-500"></i>
                                <span class="text-red-400"><?php echo $table; ?></span>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <?php if (!empty($missing_tables)): ?>
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <div class="bg-orange-900/20 border border-orange-500/30 p-3 rounded">
                            <p class="text-orange-400 text-sm font-medium mb-1">
                                <i class="ph ph-warning-circle mr-1"></i>
                                Tablas faltantes: <?php echo count($missing_tables); ?>
                            </p>
                            <div class="flex gap-2">
                                <a href="/?route=admin&page=db_init" class="text-orange-500 hover:text-orange-400 text-xs flex items-center gap-1">
                                    <i class="ph ph-database-plus"></i> Inicializar
                                </a>
                                <a href="/update_db.php" class="text-blue-500 hover:text-blue-400 text-xs flex items-center gap-1">
                                    <i class="ph ph-wrench"></i> Actualizar
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Messages -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Mensajes Recientes</h2>
                    <a href="/?route=admin&page=inbox" class="text-orange-500 hover:text-orange-400 text-sm flex items-center gap-1">
                        <i class="ph ph-envelope"></i> Ver todos
                    </a>
                </div>

                <?php
                // Get recent messages
                $recent_messages = [];
                if (!is_array($db)) {
                    $recent_result = $db->query("SELECT * FROM messages ORDER BY created_at DESC LIMIT 5");
                    if ($recent_result) {
                        while ($row = $recent_result->fetch_assoc()) {
                            $recent_messages[] = $row;
                        }
                    }
                }
                ?>

                <div class="space-y-4">
                    <?php if (empty($recent_messages)): ?>
                    <div class="text-center py-4">
                        <i class="ph ph-envelope-simple text-gray-600 text-4xl mb-2"></i>
                        <p class="text-gray-400">No hay mensajes recientes</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($recent_messages as $msg): ?>
                    <div class="flex items-start gap-3 p-3 bg-gray-700 rounded-lg">
                        <div class="bg-<?php echo $msg['status'] === 'unread' ? 'red' : ($msg['status'] === 'read' ? 'yellow' : 'green'); ?>-500 rounded-full p-2 mt-1">
                            <i class="ph ph-envelope text-white text-sm"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-white font-medium truncate"><?php echo htmlspecialchars($msg['name']); ?></p>
                                <span class="text-xs text-gray-400"><?php echo date('d/m H:i', strtotime($msg['created_at'])); ?></span>
                            </div>
                            <p class="text-sm text-gray-400 truncate"><?php echo htmlspecialchars($msg['subject']); ?></p>
                            <p class="text-xs text-gray-500 truncate"><?php echo htmlspecialchars(substr($msg['message'], 0, 60)) . (strlen($msg['message']) > 60 ? '...' : ''); ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- System Status -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Estado del Sistema</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Database Status -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Base de Datos</span>
                        <?php if (empty($critical_missing)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-warning-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo empty($critical_missing) ? 'Funcionando' : count($critical_missing) . ' tablas faltantes'; ?>
                    </p>
                </div>

                <!-- Messages System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Mensajes</span>
                        <?php if (in_array('messages', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo in_array('messages', $existing_tables) ? 'Activo' : 'No disponible'; ?>
                    </p>
                </div>

                <!-- Themes System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Temas</span>
                        <?php if (in_array('themes', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo in_array('themes', $existing_tables) ? 'Activo' : 'No disponible'; ?>
                    </p>
                </div>

                <!-- Content System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Contenido</span>
                        <?php if (in_array('content', $existing_tables) && in_array('initiatives', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo (in_array('content', $existing_tables) && in_array('initiatives', $existing_tables)) ? 'Activo' : 'Parcial'; ?>
                    </p>
                </div>
            </div>

            <?php if (!empty($missing_tables)): ?>
            <div class="mt-6 bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg">
                <div class="flex items-start gap-3">
                    <i class="ph ph-info-circle text-orange-500 text-lg mt-0.5"></i>
                    <div>
                        <p class="text-orange-400 font-medium mb-1">Funcionalidades limitadas</p>
                        <p class="text-sm text-gray-300 mb-3">
                            Algunas funciones pueden no estar disponibles debido a tablas faltantes en la base de datos.
                        </p>
                        <div class="flex gap-2">
                            <a href="/?route=admin&page=db_init" class="btn btn-primary btn-sm">
                                <i class="ph ph-database-plus"></i> Inicializar DB
                            </a>
                            <a href="/update_db.php" class="btn btn-secondary btn-sm">
                                <i class="ph ph-wrench"></i> Actualizar DB
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Gestión de Contenido</h2>

            <!-- Tabs -->
            <div x-data="{ activeTab: 'content' }">
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <button
                                @click="activeTab = 'content'"
                                :class="{ 'border-orange-500 text-orange-500': activeTab === 'content', 'border-transparent text-gray-400 hover:text-gray-300': activeTab !== 'content' }"
                                class="inline-block py-2 px-4 border-b-2 font-medium text-sm"
                            >
                                Contenido
                            </button>
                        </li>
                        <li class="mr-2">
                            <button
                                @click="activeTab = 'initiatives'"
                                :class="{ 'border-orange-500 text-orange-500': activeTab === 'initiatives', 'border-transparent text-gray-400 hover:text-gray-300': activeTab !== 'initiatives' }"
                                class="inline-block py-2 px-4 border-b-2 font-medium text-sm"
                            >
                                Iniciativas
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Content Tab -->
                <div x-show="activeTab === 'content'">
                    <button
                        class="mb-6 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                        onclick="document.getElementById('content-form').reset(); document.getElementById('content-form-title').textContent = 'Agregar Contenido';"
                    >
                        Agregar Nuevo Contenido
                    </button>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
                            <thead class="bg-gray-600">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Sección</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Título</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subtítulo</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-600">
                                <?php foreach ($content_data as $content): ?>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo $content['id']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['section']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['title']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['subtitle']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <button
                                            class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded mr-2"
                                            onclick="editContent(<?php echo htmlspecialchars(json_encode($content)); ?>)"
                                        >
                                            Editar
                                        </button>
                                        <a
                                            href="/?route=admin&action=delete&type=content&id=<?php echo $content['id']; ?>"
                                            class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded"
                                            onclick="return confirm('¿Estás seguro de que deseas eliminar este contenido?');"
                                        >
                                            Eliminar
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($content_data)): ?>
                                <tr>
                                    <td colspan="5" class="px-4 py-3 text-sm text-gray-300 text-center">No hay contenido disponible.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Content Form -->
                    <div class="mt-8 bg-gray-700 p-6 rounded-lg">
                        <h3 id="content-form-title" class="text-xl font-bold text-white mb-4">Agregar Contenido</h3>
                        <form id="content-form" method="POST" action="/?route=admin" class="space-y-4">
                            <input type="hidden" name="id" id="content-id">

                            <div>
                                <label for="section" class="block text-sm font-medium text-gray-300 mb-1">Sección</label>
                                <select
                                    id="section"
                                    name="section"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                                    <option value="hero">Hero (Inicio)</option>
                                    <option value="about">Sobre Nosotros</option>
                                    <option value="team">Equipo</option>
                                    <option value="eventos">Eventos</option>
                                </select>
                            </div>

                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-300 mb-1">Título</label>
                                <input
                                    type="text"
                                    id="title"
                                    name="title"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="subtitle" class="block text-sm font-medium text-gray-300 mb-1">Subtítulo</label>
                                <input
                                    type="text"
                                    id="subtitle"
                                    name="subtitle"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="content" class="block text-sm font-medium text-gray-300 mb-1">Contenido</label>
                                <textarea
                                    id="content"
                                    name="content"
                                    rows="5"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                ></textarea>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    name="update_content"
                                    class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                                >
                                    Guardar
                                </button>
                                <button
                                    type="button"
                                    onclick="document.getElementById('content-form').reset(); document.getElementById('content-id').value = '';"
                                    class="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded ml-2"
                                >
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Initiatives Tab -->
                <div x-show="activeTab === 'initiatives'">
                    <button
                        class="mb-6 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                        onclick="document.getElementById('initiative-form').reset(); document.getElementById('initiative-form-title').textContent = 'Agregar Iniciativa';"
                    >
                        Agregar Nueva Iniciativa
                    </button>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
                            <thead class="bg-gray-600">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nombre</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Instagram</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-600">
                                <?php foreach ($initiatives_data as $initiative): ?>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo $initiative['id']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($initiative['name']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($initiative['instagram_user']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <button
                                            class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded mr-2"
                                            onclick="editInitiative(<?php echo htmlspecialchars(json_encode($initiative)); ?>)"
                                        >
                                            Editar
                                        </button>
                                        <a
                                            href="/?route=admin&action=delete&type=initiative&id=<?php echo $initiative['id']; ?>"
                                            class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded"
                                            onclick="return confirm('¿Estás seguro de que deseas eliminar esta iniciativa?');"
                                        >
                                            Eliminar
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($initiatives_data)): ?>
                                <tr>
                                    <td colspan="4" class="px-4 py-3 text-sm text-gray-300 text-center">No hay iniciativas disponibles.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Initiative Form -->
                    <div class="mt-8 bg-gray-700 p-6 rounded-lg">
                        <h3 id="initiative-form-title" class="text-xl font-bold text-white mb-4">Agregar Iniciativa</h3>
                        <form id="initiative-form" method="POST" action="/?route=admin" class="space-y-4">
                            <input type="hidden" name="id" id="initiative-id">

                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Nombre</label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="instagram_user" class="block text-sm font-medium text-gray-300 mb-1">Usuario de Instagram</label>
                                <input
                                    type="text"
                                    id="instagram_user"
                                    name="instagram_user"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="instagram_link" class="block text-sm font-medium text-gray-300 mb-1">Enlace de Instagram</label>
                                <input
                                    type="url"
                                    id="instagram_link"
                                    name="instagram_link"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-1">Descripción</label>
                                <textarea
                                    id="description"
                                    name="description"
                                    rows="3"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                ></textarea>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    name="update_initiative"
                                    class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                                >
                                    Guardar
                                </button>
                                <button
                                    type="button"
                                    onclick="document.getElementById('initiative-form').reset(); document.getElementById('initiative-id').value = '';"
                                    class="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded ml-2"
                                >
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function editContent(content) {
        document.getElementById('content-id').value = content.id;
        document.getElementById('section').value = content.section;
        document.getElementById('title').value = content.title;
        document.getElementById('subtitle').value = content.subtitle || '';
        document.getElementById('content').value = content.content || '';
        document.getElementById('content-form-title').textContent = 'Editar Contenido';

        // Scroll to form
        document.getElementById('content-form').scrollIntoView({ behavior: 'smooth' });
    }

    function editInitiative(initiative) {
        document.getElementById('initiative-id').value = initiative.id;
        document.getElementById('name').value = initiative.name;
        document.getElementById('instagram_user').value = initiative.instagram_user;
        document.getElementById('instagram_link').value = initiative.instagram_link || '';
        document.getElementById('description').value = initiative.description || '';
        document.getElementById('initiative-form-title').textContent = 'Editar Iniciativa';

        // Scroll to form
        document.getElementById('initiative-form').scrollIntoView({ behavior: 'smooth' });
    }
</script>
