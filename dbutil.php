<?php
/**
 * Database Utility Script
 * 
 * This script helps apply database schema changes to existing databases
 * without losing existing data. It's designed for updating production
 * databases with new tables, columns, and other schema modifications.
 * 
 * Usage: Run this script via web browser or command line to update
 * an existing database with the latest schema changes.
 */

// Prevent direct access in production
if (!defined('DB_UTIL_ALLOWED')) {
    // Only allow access if explicitly enabled or in development
    $allowed_ips = ['127.0.0.1', '::1', 'localhost'];
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (!in_array($client_ip, $allowed_ips) && !isset($_GET['allow'])) {
        die('Access denied. This utility is only available in development environment.');
    }
}

require_once 'includes/db.php';

// Database migration versions and their corresponding changes
$migrations = [
    '1.0.0' => [
        'description' => 'Initial database setup',
        'tables' => ['users', 'content', 'initiatives', 'events', 'contacts', 'themes'],
        'changes' => []
    ],
    '1.1.0' => [
        'description' => 'Add messages table for contact form submissions',
        'tables' => ['messages'],
        'changes' => [
            'add_messages_table' => "CREATE TABLE IF NOT EXISTS `messages` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `name` VARCHAR(255) NOT NULL,
                `email` VARCHAR(255) NOT NULL,
                `subject` VARCHAR(255) NOT NULL,
                `message` TEXT NOT NULL,
                `instagram` VARCHAR(100),
                `twitter` VARCHAR(100),
                `status` ENUM('unread', 'read', 'replied') DEFAULT 'unread',
                `ip_address` VARCHAR(45),
                `user_agent` TEXT,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )"
        ]
    ],
    '1.2.0' => [
        'description' => 'Add database version tracking',
        'tables' => ['db_migrations'],
        'changes' => [
            'add_migrations_table' => "CREATE TABLE IF NOT EXISTS `db_migrations` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `version` VARCHAR(20) NOT NULL UNIQUE,
                `description` TEXT,
                `applied_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
        ]
    ]
];

// Current target version
$target_version = '1.2.0';

// HTML output flag
$html_output = !isset($argv) && php_sapi_name() !== 'cli';

if ($html_output) {
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Database Utility - ABLA</title>
        <style>
            body { 
                font-family: 'Courier New', monospace; 
                background: #1a1a1a; 
                color: #fff; 
                margin: 0; 
                padding: 20px; 
                line-height: 1.6;
            }
            .container { 
                max-width: 1200px; 
                margin: 0 auto; 
                background: #2a2a2a; 
                padding: 30px; 
                border-radius: 10px; 
                box-shadow: 0 0 20px rgba(0,0,0,0.5);
            }
            h1 { 
                color: #f7931a; 
                text-align: center; 
                margin-bottom: 30px;
                font-size: 2.5em;
            }
            h2 { 
                color: #f7931a; 
                border-bottom: 2px solid #f7931a; 
                padding-bottom: 10px;
            }
            .success { 
                background: #1a4a1a; 
                border: 1px solid #4a8a4a; 
                color: #8af88a; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .error { 
                background: #4a1a1a; 
                border: 1px solid #8a4a4a; 
                color: #f88a8a; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .warning { 
                background: #4a4a1a; 
                border: 1px solid #8a8a4a; 
                color: #f8f88a; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .info { 
                background: #1a1a4a; 
                border: 1px solid #4a4a8a; 
                color: #8a8af8; 
                padding: 15px; 
                border-radius: 5px; 
                margin: 10px 0;
            }
            .code { 
                background: #000; 
                border: 1px solid #444; 
                padding: 15px; 
                border-radius: 5px; 
                overflow-x: auto; 
                margin: 10px 0;
                font-family: 'Courier New', monospace;
            }
            .btn { 
                background: #f7931a; 
                color: #000; 
                padding: 12px 24px; 
                border: none; 
                border-radius: 5px; 
                cursor: pointer; 
                font-weight: bold; 
                text-decoration: none; 
                display: inline-block; 
                margin: 10px 5px;
            }
            .btn:hover { 
                background: #ff9500; 
            }
            .btn-danger { 
                background: #dc3545; 
                color: #fff; 
            }
            .btn-danger:hover { 
                background: #c82333; 
            }
            table { 
                width: 100%; 
                border-collapse: collapse; 
                margin: 20px 0; 
            }
            th, td { 
                border: 1px solid #444; 
                padding: 12px; 
                text-align: left; 
            }
            th { 
                background: #333; 
                color: #f7931a; 
            }
            .status-applied { color: #4CAF50; }
            .status-pending { color: #FF9800; }
            .status-error { color: #F44336; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🗄️ Database Utility - ABLA</h1>
    <?php
}

/**
 * Output a message with appropriate formatting
 */
function output($message, $type = 'info') {
    global $html_output;
    
    if ($html_output) {
        echo "<div class=\"$type\">$message</div>\n";
    } else {
        $prefix = match($type) {
            'success' => '[✓] ',
            'error' => '[✗] ',
            'warning' => '[!] ',
            default => '[i] '
        };
        echo $prefix . strip_tags($message) . "\n";
    }
}

/**
 * Check if a table exists in the database
 */
function tableExists($db, $tableName) {
    $result = $db->query("SHOW TABLES LIKE '$tableName'");
    return $result && $result->num_rows > 0;
}

/**
 * Check if a column exists in a table
 */
function columnExists($db, $tableName, $columnName) {
    $result = $db->query("SHOW COLUMNS FROM `$tableName` LIKE '$columnName'");
    return $result && $result->num_rows > 0;
}

/**
 * Get current database version
 */
function getCurrentVersion($db) {
    if (!tableExists($db, 'db_migrations')) {
        return '1.0.0'; // Default version if migrations table doesn't exist
    }
    
    $result = $db->query("SELECT version FROM db_migrations ORDER BY applied_at DESC LIMIT 1");
    if ($result && $row = $result->fetch_assoc()) {
        return $row['version'];
    }
    
    return '1.0.0';
}

/**
 * Record a migration as applied
 */
function recordMigration($db, $version, $description) {
    if (!tableExists($db, 'db_migrations')) {
        return false;
    }
    
    $stmt = $db->prepare("INSERT INTO db_migrations (version, description) VALUES (?, ?) ON DUPLICATE KEY UPDATE applied_at = CURRENT_TIMESTAMP");
    if ($stmt) {
        $stmt->bind_param("ss", $version, $description);
        $result = $stmt->execute();
        $stmt->close();
        return $result;
    }
    
    return false;
}

/**
 * Apply a specific migration
 */
function applyMigration($db, $version, $migration) {
    $errors = [];
    $success_count = 0;
    
    output("Aplicando migración $version: {$migration['description']}", 'info');
    
    // Apply each change in the migration
    foreach ($migration['changes'] as $change_name => $sql) {
        output("Ejecutando: $change_name", 'info');
        
        if ($html_output) {
            echo "<div class=\"code\">$sql</div>";
        }
        
        $result = $db->query($sql);
        
        if ($result) {
            output("✓ $change_name aplicado correctamente", 'success');
            $success_count++;
        } else {
            $error_msg = "✗ Error en $change_name: " . $db->error;
            output($error_msg, 'error');
            $errors[] = $error_msg;
        }
    }
    
    // Record migration if successful
    if (empty($errors)) {
        if (recordMigration($db, $version, $migration['description'])) {
            output("Migración $version registrada correctamente", 'success');
        } else {
            output("Advertencia: No se pudo registrar la migración $version", 'warning');
        }
        return true;
    }
    
    return false;
}

/**
 * Main execution
 */
try {
    // Connect to database
    $db = connectDB();
    
    if (is_array($db)) {
        output("Error de conexión a la base de datos: " . $db['error'], 'error');
        exit(1);
    }
    
    output("Conectado a la base de datos correctamente", 'success');
    
    // Get current version
    $current_version = getCurrentVersion($db);
    output("Versión actual de la base de datos: $current_version", 'info');
    output("Versión objetivo: $target_version", 'info');
    
    // Check if we need to run migrations
    if (version_compare($current_version, $target_version, '>=')) {
        output("La base de datos ya está actualizada a la versión $target_version o superior", 'success');
    } else {
        output("Actualizando base de datos de $current_version a $target_version", 'warning');
        
        // Show migration plan
        if ($html_output) {
            echo "<h2>Plan de Migración</h2>";
            echo "<table>";
            echo "<tr><th>Versión</th><th>Descripción</th><th>Estado</th></tr>";
            
            foreach ($migrations as $version => $migration) {
                $status = version_compare($version, $current_version, '>') ? 'pending' : 'applied';
                $status_text = $status === 'applied' ? 'Aplicada' : 'Pendiente';
                $status_class = "status-$status";
                
                echo "<tr>";
                echo "<td>$version</td>";
                echo "<td>{$migration['description']}</td>";
                echo "<td class=\"$status_class\">$status_text</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        // Apply migrations
        $applied_migrations = 0;
        $failed_migrations = 0;
        
        foreach ($migrations as $version => $migration) {
            if (version_compare($version, $current_version, '>') && version_compare($version, $target_version, '<=')) {
                if (applyMigration($db, $version, $migration)) {
                    $applied_migrations++;
                } else {
                    $failed_migrations++;
                    output("Falló la migración $version. Deteniendo el proceso.", 'error');
                    break;
                }
            }
        }
        
        // Summary
        if ($html_output) {
            echo "<h2>Resumen de Migración</h2>";
        }
        
        if ($failed_migrations > 0) {
            output("Migración completada con errores. $applied_migrations migraciones aplicadas, $failed_migrations fallaron.", 'error');
        } elseif ($applied_migrations > 0) {
            output("Migración completada exitosamente. $applied_migrations migraciones aplicadas.", 'success');
        } else {
            output("No se aplicaron migraciones.", 'info');
        }
    }
    
    // Show current database status
    if ($html_output) {
        echo "<h2>Estado Actual de la Base de Datos</h2>";
        echo "<table>";
        echo "<tr><th>Tabla</th><th>Estado</th><th>Registros</th></tr>";
        
        $all_tables = array_unique(array_merge(...array_column($migrations, 'tables')));
        
        foreach ($all_tables as $table) {
            $exists = tableExists($db, $table);
            $status = $exists ? 'Existe' : 'No existe';
            $status_class = $exists ? 'status-applied' : 'status-error';
            
            $count = 0;
            if ($exists) {
                $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
                if ($count_result) {
                    $count = $count_result->fetch_assoc()['count'];
                }
            }
            
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td class=\"$status_class\">$status</td>";
            echo "<td>" . ($exists ? number_format($count) : 'N/A') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Show available actions
    if ($html_output) {
        echo "<h2>Acciones Disponibles</h2>";
        echo "<div>";
        echo "<a href=\"?action=check\" class=\"btn\">Verificar Estado</a>";
        echo "<a href=\"?action=force_migrate\" class=\"btn btn-danger\" onclick=\"return confirm('¿Estás seguro de que quieres forzar todas las migraciones? Esto puede sobrescribir datos existentes.')\">Forzar Migración</a>";
        echo "<a href=\"/?route=admin\" class=\"btn\">Ir al Admin</a>";
        echo "</div>";
        
        // Handle actions
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'force_migrate':
                    output("Forzando aplicación de todas las migraciones...", 'warning');
                    foreach ($migrations as $version => $migration) {
                        applyMigration($db, $version, $migration);
                    }
                    break;
                    
                case 'check':
                    output("Verificación de estado completada", 'info');
                    break;
            }
        }
    }
    
} catch (Exception $e) {
    output("Error crítico: " . $e->getMessage(), 'error');
    if ($html_output) {
        echo "<div class=\"code\">" . $e->getTraceAsString() . "</div>";
    }
    exit(1);
}

if ($html_output) {
    ?>
        </div>
        <script>
            // Auto-refresh every 30 seconds if migrations are running
            if (window.location.search.includes('action=')) {
                setTimeout(() => {
                    window.location.href = window.location.pathname;
                }, 30000);
            }
        </script>
    </body>
    </html>
    <?php
}
?>
