<?php
/**
 * The base configuration for ABLA
 *
 * This file contains the following configurations:
 * * Database settings
 * * Secret keys
 * * Site URL
 * * Debug mode
 *
 * @link https://abla.lat/
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for ABLA */
define('DB_NAME', 'abla_db');

/** Database username */
define('DB_USER', 'root');

/** Database password */
define('DB_PASSWORD', '');

/** Database hostname */
define('DB_HOST', 'localhost');

/** Database charset to use in creating database tables. */
define('DB_CHARSET', 'utf8mb4');

/** The database collate type. Don't change this if in doubt. */
define('DB_COLLATE', '');

/**
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * a random string generator or the WordPress.org secret-key service.
 */
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
define('LOGGED_IN_KEY',    'put your unique phrase here');
define('NONCE_KEY',        'put your unique phrase here');

/**
 * ABLA Site URL
 *
 * The base URL of your ABLA installation.
 */
define('SITE_URL', 'http://localhost:8888');

/**
 * For developers: ABLA debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 */
define('ABLA_DEBUG', false);

/**
 * Database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 */
$table_prefix = 'abla_';

/* That's all, stop editing! Happy publishing. */

// Create database configuration array
$GLOBALS['db_config'] = [
    'host'     => DB_HOST,
    'username' => DB_USER,
    'password' => DB_PASSWORD,
    'database' => DB_NAME,
    'charset'  => DB_CHARSET,
    'collate'  => DB_COLLATE,
    'prefix'   => $table_prefix
];

// Set error reporting based on debug mode
if (ABLA_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Define site constants
define('ABLA_VERSION', '1.0.0');
define('ABLA_ROOT', dirname(__FILE__));
define('ABLA_CONTENT_DIR', ABLA_ROOT . '/content');
define('ABLA_ADMIN_DIR', ABLA_ROOT . '/admin');
define('ABLA_INCLUDES_DIR', ABLA_ROOT . '/includes');

// Define URL constants
define('ABLA_CONTENT_URL', SITE_URL . '/content');
define('ABLA_ADMIN_URL', SITE_URL . '/admin');
define('ABLA_ASSETS_URL', SITE_URL . '/assets');
