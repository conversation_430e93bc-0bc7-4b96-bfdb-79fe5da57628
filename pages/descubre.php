<?php
// Get initiatives from database
$db = connectDB();
$initiatives = [];
$result = $db->query("SELECT * FROM initiatives ORDER BY id DESC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $initiatives[] = $row;
    }
}
?>

<section class="bg-black text-white min-h-screen py-16 px-4">
    <div class="container mx-auto max-w-4xl">
        <!-- Título principal con "ABLA" en naranja -->
        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
            DESCUBRE <span class="text-orange-500">ABLA</span>
        </h1>

        <!-- Cita en "nube" -->
        <div class="relative bg-gray-800 rounded-3xl p-6 mb-12">
            <div class="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
            <p class="text-xl italic">
                "Bitcoin es la primera herramienta monetaria que no puede ser confiscada, censurada o devaluada por ningún gobierno o institución." – Michael Saylor
            </p>
        </div>

        <!-- Grid de iniciativas -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <?php foreach ($initiatives as $initiative): ?>
            <a href="<?php echo htmlspecialchars($initiative['instagram_link']); ?>" target="_blank" class="bg-gray-900 p-6 rounded-xl hover:bg-gray-800 transition-colors">
                <!-- Nombre de la iniciativa -->
                <h3 class="text-xl font-bold mb-2"><?php echo htmlspecialchars($initiative['name']); ?></h3>

                <!-- Usuario de Instagram -->
                <p class="text-orange-500 mb-4"><?php echo htmlspecialchars($initiative['instagram_user']); ?></p>

                <!-- Descripción -->
                <div class="text-gray-300 text-sm mb-4 flex-grow overflow-hidden initiative-description">
                    <?php echo $initiative['description']; ?>
                </div>

                <!-- Sección de Últimas Fotos (Placeholders) -->
                <div class="grid grid-cols-3 gap-2 mt-4">
                    <!-- Placeholder para Foto 1 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                    <!-- Placeholder para Foto 2 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                    <!-- Placeholder para Foto 3 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                </div>
            </a>
            <?php endforeach; ?>

            <?php if (empty($initiatives)): ?>
            <div class="col-span-2 text-center py-8">
                <p>No hay iniciativas disponibles en este momento.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
