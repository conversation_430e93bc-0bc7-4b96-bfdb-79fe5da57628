<?php
// Get initiatives from database
$db = connectDB();
$initiatives = [];
$result = $db->query("SELECT * FROM initiatives ORDER BY id DESC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $initiatives[] = $row;
    }
}
?>

<section class="bg-black text-white min-h-screen py-16 px-4">
    <div class="container mx-auto max-w-4xl">
        <!-- Título principal con "ABLA" en naranja -->
        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
            DESCUBRE <span class="text-orange-500">ABLA</span>
        </h1>

        <!-- Cita en "nube" -->
        <div class="relative bg-gray-800 rounded-3xl p-6 mb-12">
            <div class="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
            <p class="text-xl italic">
                "Bitcoin es la primera herramienta monetaria que no puede ser confiscada, censurada o devaluada por ningún gobierno o institución." – <PERSON> Saylor
            </p>
        </div>

        <!-- Grid de iniciativas -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <?php foreach ($initiatives as $initiative): ?>
            <div class="bg-gray-900 p-6 rounded-xl hover:bg-gray-800 transition-colors">
                <!-- Avatar and Header -->
                <div class="flex items-center mb-4">
                    <?php
                    require_once 'includes/avatar_fetcher.php';
                    $avatar_url = getAvatarForDisplay($initiative);
                    ?>
                    <img src="<?php echo htmlspecialchars($avatar_url); ?>"
                         alt="<?php echo htmlspecialchars($initiative['name']); ?>"
                         class="w-12 h-12 rounded-full object-cover border-2 border-orange-500/30 mr-4"
                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNGNzkzMUEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0QzE0IDUuMSAxMy4xIDYgMTIgNkMxMC45IDYgMTAgNS4xIDEwIDRDMTAgMi45IDEwLjkgMiAxMiAyWk0yMSAxOVYyMEgzVjE5QzMgMTYuMzMgOC42NyAxNSAxMiAxNUMxNS4zMyAxNSAyMSAxNi4zMyAyMSAxOVpNMTIgMTNDOS43OSAxMyA4IDExLjIxIDggOUM4IDYuNzkgOS43OSA1IDEyIDVDMTQuMjEgNSAxNiA2Ljc5IDE2IDlDMTYgMTEuMjEgMTQuMjEgMTMgMTIgMTNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+'">

                    <div>
                        <h3 class="text-xl font-bold mb-1"><?php echo htmlspecialchars($initiative['name']); ?></h3>
                        <div class="flex items-center gap-3 text-sm">
                            <?php if (!empty($initiative['instagram_user'])): ?>
                                <span class="text-pink-500 flex items-center gap-1">
                                    <i class="ph ph-instagram-logo"></i>
                                    <?php echo htmlspecialchars($initiative['instagram_user']); ?>
                                </span>
                            <?php endif; ?>

                            <?php if (!empty($initiative['twitter_user'])): ?>
                                <span class="text-blue-400 flex items-center gap-1">
                                    <i class="ph ph-x-logo"></i>
                                    <?php echo htmlspecialchars($initiative['twitter_user']); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Descripción -->
                <div class="text-gray-300 text-sm mb-4 flex-grow overflow-hidden initiative-description">
                    <?php echo $initiative['description']; ?>
                </div>

                <!-- Social Links -->
                <div class="flex gap-2 mb-4">
                    <?php if (!empty($initiative['instagram_link'])): ?>
                        <a href="<?php echo htmlspecialchars($initiative['instagram_link']); ?>" target="_blank" class="bg-pink-600 hover:bg-pink-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1">
                            <i class="ph ph-instagram-logo"></i> Instagram
                        </a>
                    <?php endif; ?>

                    <?php if (!empty($initiative['twitter_link'])): ?>
                        <a href="<?php echo htmlspecialchars($initiative['twitter_link']); ?>" target="_blank" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1">
                            <i class="ph ph-x-logo"></i> X
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Sección de Últimas Fotos (Placeholders) -->
                <div class="grid grid-cols-3 gap-2 mt-4">
                    <!-- Placeholder para Foto 1 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                    <!-- Placeholder para Foto 2 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                    <!-- Placeholder para Foto 3 -->
                    <div class="w-full h-20 bg-gray-700 rounded-md">
                        <!-- Aquí iría la imagen del feed -->
                    </div>
                </div>
            </div>
            <?php endforeach; ?>

            <?php if (empty($initiatives)): ?>
            <div class="col-span-2 text-center py-8">
                <p>No hay iniciativas disponibles en este momento.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
