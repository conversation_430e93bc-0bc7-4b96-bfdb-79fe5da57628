<?php
// Process form submission
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_submit'])) {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $message_text = $_POST['message'] ?? '';
    $interest = $_POST['interest'] ?? '';
    
    if (empty($name) || empty($email) || empty($message_text)) {
        $message = 'Por favor completa todos los campos requeridos.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Por favor ingresa un correo electrónico válido.';
    } else {
        // Here you would typically send an email or store in database
        // For now, we'll just simulate success
        $success = true;
        $message = '¡Gracias por contactarnos! Te responderemos a la brevedad.';
        
        // Optional: Store in database
        $db = connectDB();
        $stmt = $db->prepare("INSERT INTO contacts (name, email, message, interest) VALUES (?, ?, ?, ?)");
        if ($stmt) {
            $stmt->bind_param("ssss", $name, $email, $message_text, $interest);
            $stmt->execute();
            $stmt->close();
        }
    }
}
?>

<section class="bg-black text-white min-h-screen py-16 px-4">
    <div class="container mx-auto max-w-4xl">
        <!-- Título principal -->
        <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold uppercase mb-12">
            SÚMATE
        </h1>

        <!-- Cita en "nube" -->
        <div class="relative bg-gray-800 rounded-3xl p-6 mb-12">
            <div class="absolute -top-4 left-8 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-gray-800 border-r-[20px] border-r-transparent"></div>
            <p class="text-xl italic">
                "La adopción de Bitcoin en América Latina no es solo una oportunidad económica, es una revolución social que empodera a millones de personas."
            </p>
        </div>

        <!-- Mensaje de éxito o error -->
        <?php if (!empty($message)): ?>
        <div class="mb-8 p-4 rounded-lg <?php echo $success ? 'bg-green-800' : 'bg-red-800'; ?>">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <!-- Formulario de contacto -->
        <div class="bg-gray-900 p-8 rounded-xl mb-12">
            <h2 class="text-3xl font-bold mb-6">Contáctanos</h2>
            
            <?php if ($success): ?>
            <p class="mb-4">Tu mensaje ha sido enviado. ¡Gracias por contactarnos!</p>
            <a href="/?route=home" class="inline-block bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded transition-colors">
                Volver al inicio
            </a>
            <?php else: ?>
            <form method="POST" action="/?route=sumate" class="space-y-6">
                <div>
                    <label for="name" class="block mb-2">Nombre completo *</label>
                    <input 
                        type="text" 
                        id="name" 
                        name="name" 
                        required 
                        class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-orange-500"
                    >
                </div>
                
                <div>
                    <label for="email" class="block mb-2">Correo electrónico *</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        required 
                        class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-orange-500"
                    >
                </div>
                
                <div>
                    <label for="interest" class="block mb-2">¿Cómo te gustaría colaborar?</label>
                    <select 
                        id="interest" 
                        name="interest" 
                        class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-orange-500"
                    >
                        <option value="volunteer">Voluntariado</option>
                        <option value="donation">Donación</option>
                        <option value="partnership">Alianza estratégica</option>
                        <option value="other">Otro</option>
                    </select>
                </div>
                
                <div>
                    <label for="message" class="block mb-2">Mensaje *</label>
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="5" 
                        required 
                        class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-orange-500"
                    ></textarea>
                </div>
                
                <div>
                    <button 
                        type="submit" 
                        name="contact_submit" 
                        class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
                    >
                        Enviar mensaje
                    </button>
                </div>
            </form>
            <?php endif; ?>
        </div>

        <!-- Otras formas de sumarse -->
        <div>
            <h2 class="text-3xl font-bold mb-6">Otras formas de sumarte</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-900 p-6 rounded-xl">
                    <h3 class="text-xl font-bold mb-3 text-orange-500">Donaciones</h3>
                    <p class="text-gray-300 mb-4">
                        Apoya nuestras iniciativas con una donación en Bitcoin. Cada satoshi cuenta para seguir impulsando la adopción en la región.
                    </p>
                    <div class="bg-gray-800 p-4 rounded-lg text-center">
                        <p class="mb-2 text-sm">Escanea el código QR o copia la dirección:</p>
                        <div class="w-32 h-32 mx-auto mb-2 bg-white p-2 rounded-lg">
                            <!-- Placeholder para QR code -->
                            <div class="w-full h-full bg-gray-200"></div>
                        </div>
                        <code class="text-xs break-all bg-gray-700 p-2 rounded">******************************************</code>
                    </div>
                </div>
                
                <div class="bg-gray-900 p-6 rounded-xl">
                    <h3 class="text-xl font-bold mb-3 text-orange-500">Voluntariado</h3>
                    <p class="text-gray-300 mb-4">
                        Únete como voluntario y contribuye con tus habilidades y tiempo a nuestros proyectos e iniciativas.
                    </p>
                    <ul class="list-disc list-inside text-gray-300 space-y-2">
                        <li>Educación y divulgación</li>
                        <li>Desarrollo de software</li>
                        <li>Diseño y comunicación</li>
                        <li>Organización de eventos</li>
                        <li>Traducción de contenidos</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
